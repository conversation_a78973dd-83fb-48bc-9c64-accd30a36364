package com.facishare.crm.sfa.utilities.util;

/**
 * Created by wangmy on 2019/1/11 19:27.
 */
public interface SFAI18NKeyUtil {

    /**
     * 分配销售线索
     */
    String SFA_TITLE_ALLOCATE_LEADS = "sfa.Enums.1460.1";


    /**
     * 分配销售线索 操作人： {0}
     */
    String SFA_TITLE_ALLOCATE_LEADS_NEW_RECORD = "sfa.Enums.1460.1.new.record";

    /**
     * 线索被领取
     */
    String SFA_TITLE_CHOOSE_LEADS = "sfa.Enums.1436.1";

    /**
     * 成功分发给组织：%s
     */
    String SFA_ACCOUNT_MAIN_DATA_ALLOCATE_SUCCESS = "sfa_account_main_data_allocate_success";

    /**
     * 客户负责人变更
     */
    String SFA_TITLE_CHANGE_OWNER_ACCOUNT = "sfa.Enums.1073.1";

    /**
     * 客户被收回到公海
     */
    String SFA_TITLE_TAKE_BACK_ACCOUNT = "sfa.Enums.1067.1";


    /**
     * 客户报备已通过
     */
    String SFA_SUBMISSION_HAS_PASSED_ACCOUNT = "sfa.Enums.1055.1";

    /**
     * 客户报备未通过
     */
    String SFA_SUBMISSION_HAS_NOT_PASSED_ACCOUNT = "sfa.Enums.1061.1";

    /**
     * 截止日期必须大于开始日期！
     */
    String SFA_CONTRACTOBJ_DATACONTRAST = "sfa.contractobj.datacontrast";
    /**
     * 纬度值有误！
     */
    String SFA_ACCOUNTADDR_ERRORINLATITUDE = "sfa.accountaddr.errorinlatitude";
    /**
     * 经度值有误！
     */
    String SFA_ACCOUNTADDR_ERRORINLONGITUDE = "sfa.accountaddr.errorinlongitude";
    /**
     * 国家、省、市、区不能为空！
     */
    String SFA_ACCOUNTADDR_AREANOTNULL = "sfa.accountaddr.areanotnull";
    /**
     * 客户ID无效或者已经被删除!
     */
    String SFA_ACCOUNTADDR_ACOUNTNOTNULL = "sfa.accountaddr.acountnotnull";
    /**
     * 新建客户地址限制500条!
     */
    String SFA_ACCOUNTADDR_NUM_LIMIT_500 = "sfa.accountaddr.num.limit.500";
    /**
     * 从对象的数量限制
     * {0}的数量不能超过{1}条
     */
    String SFA_FROM_OBJECT_NUM_LIMIT = "sfa.from.object.num.limit";
    /**
     * {objname}不存在或已被删除!
     */
    String SFA_ACCOUNTADDR_CONTACTNOTNULL = "sfa.accountaddr.contactnotnull";
    /**
     * 该客户下不存在此联系人!
     */
    String SFA_ACCOUNTADDR_ACCOUNTNOTEXITCONTACT = "sfa.accountaddr.accountnotexitcontact";
    /**
     * 地址类型有误!
     */
    String SFA_ACCOUNTADDR_INCORRECTADDRESSTYPE = "sfa.accountaddr.incorrectaddresstype";
    /**
     * 地址信息不存在或已删除!
     */
    String SFA_ACCOUNTADDR_ACOUNTADDNOTNULL = "sfa.accountaddr.acountaddnotnull";
    /**
     * 主地址不能作废!
     */
    String SFA_ACCOUNTADDR_ACOUNTADDMAINNOTALLOWINVALID = "sfa.accountaddr.acountaddrmainnotallowinvalid";
    /**
     * 暂不支持批量删除!
     */
    String SFA_ACCOUNTADDR_BATCHDELETIONNOTSUPPORTED = "sfa.accountaddr.batchdeletionnotsupported";

    /**
     * 默认财务信息不能作废!
     */
    String SFA_ACCOUNTFININFO_DEFAULTNOTALLOWINVALID = "sfa.accountfininfo.defaultnotallowinvalid";
    /**
     * 财务信息ID无效或者已经被删除!
     */
    String SFA_ACCOUNTFININFO_ACCOUNTFININFONOTNULL = "sfa.accountfininfo.accountfininfonotnull";
    /**
     * 财务类型有误!
     */
    String SFA_ACCOUNTFININFO_INCORRECTFINANCETYPE = "sfa.accountfininfo.incorrectfinancetype";
    /**
     * 作废数据不能为空!
     */
    String SFA_ACCOUNTADDR_INVALIDDATANOTNULL = "sfa.accountaddr.invaliddatanotnull";
    /**
     * 财务不存在或已删除!
     */
    String SFA_ACCOUNTFININFO_ACOUNTADDNOTNULL = "sfa.accountfininfo.accountfininfonotnull";
    /**
     * 参数异常!
     */
    String SFA_CONTACT_PARAM_EXCEPTION = "sfa.contact.param_exception";
    /**
     * 关键决策人填写不对!
     */
    String SFA_CONTACT_ERRORPRIMARYCONTACT = "sfa.contact.errorprimarycontact";
    /**
     * 生日的格式不正确!
     */
    String SFA_CONTACT_ERRORDATAOFBIRTH = "sfa.contact.errordataofbirth";
    /**
     * 所选{0}存在相关对象数据，确定要作废这些{1}吗？
     */
    String SFA_ACCOUNT_BULKINVALIDINFO = "sfa.account.bulkinvalidinfo";

    /**
     * 相关对象{0}存在数据，确定要作废这个{1}吗？
     */
    String SFA_ACCOUNT_SINGLEINVALIDINFO = "sfa.account.singleinvalidinfo";
    /**
     * 参数错误！
     */
    String SFA_CONFIG_PARAMETER_ERROR = "sfa.config.parametererror";

    /**
     * 修改配置失敗！
     */
    String SFA_CONFIG_FAILURE = "sfa.config.failure";
    /**
     * 获取描述失败！
     */
    String SFA_CONFIG_GETDESCRIPTIONFAILED = "sfa.config.getdescriptionfailed";
    /**
     * 获取描述失败！请重新尝试！
     */
    String SFA_ACCOUNT_GETDESCRIPTIONFAILED = "sfa.account.getdescriptionfailed";
    /**
     * 不允许禁用价目表！
     */
    String SFA_CONFIG_NOTALLOWFORBIDDENPRICEBOOK = "sfa.config.notallowforbiddenpricebook";
    /**
     * 已启用快消行业包的行业价目表，无法启用标准价目表！
     */
    String SFA_CONFIG_NOTALLOWENABLEPRICEBOOK = "sfa.config.notallowEnablepricebook";
    /**
     * 开启库存后才可以开启批次与序列号管理
     */
    String SFA_CONFIG_MUST_OPEN_STOCK_BEFORE_OPEN_SN = "sfa.config.must.open.stock.before.open.sn";

    /**
     * 不允许禁用行业价目表!
     */
    String SFA_CONFIG_CANT_DISABLE_BU_PRICE_BOOK = "sfa.config.cant.disable.bu.price.book";
    /**
     * 已启用订货通，不允许更改此配置!
     */
    String SFA_CONFIG_OPEN_DHT_CANT_CHANGE_THE_CONFIG = "sfa.config.open.dht.cant.change.the.config";

    /**
     * 不允许禁用批次与序列号管理！
     */
    String SFA_CONFIG_CANT_DISABLE_SN_MANAGEMENT = "sfa.config.cant.disable.sn.management";

    /**
     * 不允许禁用客户账户！
     */
    String SFA_CONFIG_NOTALLOWFORBIDDENCUSTOMERACCOUNT = "sfa.config.notallowforbiddencustomeraccount";

    /**
     * 不允许禁用设备管理！
     */
    String SFA_CONFIG_NOT_ALL_CLOSE_DEVICE_MANAGEMENT = "sfa.config.cant.close.device.management";

    /**
     * 不允许禁用库存！
     */
    String SFA_CONFIG_NOTALLOWFORBIDDENINVENTORY = "sfa.config.notallowforbiddeninventory";
    /**
     * 不允许禁用促销！
     */
    String SFA_CONFIG_NOTALLOWFORBIDDENPROMOTION = "sfa.config.notallowforbiddenpromotion";
    /**
     * 不允许禁用发货单！
     */
    String SFA_CONFIG_NOTALLOWFORBIDDENDELIVERYNOTE = "sfa.config.notallowforbiddendeliverynote";
    /**
     * 已启用促销，无法启用属性！
     */
    String SFA_CONFIG_NOTOPENATTRIBUTEFORPROMOTION = "sfa.config.notopenattributeforpromotion";
    /**
     * 未开启价目表，无法启用属性!
     */
    String SFA_CONFIG_NOTOPENATTRIBUTEFORPRICEBOOK = "sfa.config.notopenattributeforpricebook";
    /**
     * 已启用多单位，无法启用属性!
     */
    String SFA_CONFIG_NOTOPENATTRIBUTEFORMULTIUNIT = "sfa.config.notopenattributeformultiunit";
    /**
     * 已启用商品设置，无法启用属性!
     */
    String SFA_CONFIG_NOTOPENATTRIBUTEFORSPU = "sfa.config.notopenattributeforspu";
    /**
     * 未启用产品明细支持复制开关，无法启用属性!
     */
    String SFA_CONFIG_NOTOPENATTRIBUTEFORPRODUCTCOPY = "sfa.config.notopenattributeforproductcopy";

    /**
     * 不允许禁用合作伙伴！
     */
    String SFA_CONFIG_CANT_CLOSE_PARTNER = "sfa.config.cant.close.partner";
    /**
     * 不允许禁用设备管理！
     */
    String SFA_CONFIG_NOTALLOWFORBIDDENDEVICEMANAGEMENT = "sfa.config.notallowforbiddendevicemanagement";
    /**
     * 不允许禁用商机2.0!
     */
    String SFA_CONFIG_CANT_CLOSE_NEW_OPP = "sfa.config.cant.close.new_opp";

    /**
     * 不允许禁用{0}！
     */
    String SFA_CONFIG_NOTALLOWFORBIDDENOBJECT = "sfa.config.notallowforbiddenobject";
    /**
     * 无权导入联系人通讯录！
     */
    String SFA_CONTACT_NOPERMISSIONSTOADDRESSBOOK = "sfa.contact.nopermissionstoaddressbook";
    /**
     * 请选择要导入的通讯录列表！
     */
    String SFA_CONTACT_NOADDRESSBOOK = "sfa.contact.noaddressbook";
    /**
     * 对象被锁定,不允许进行合并操作！
     */
    String SFA_CONTACT_ISLOCKED = "sfa.contact.islocked";
    /**
     * 联系人被合并
     */
    String SFA_CONTACT_ISMERGE = "sfa.contact.ismerge";
    /**
     * 您的{0}“{1}”被合并到"{2}"，您已成为合并后的销售团队成员，负责人：{3}，操作人：{4}
     */
    String SFA_CONTACT_MERGEMSGREMINDCONTENTISTEAMMEMBER = "sfa.contact.mergemsgremindcontentisteammember";
    /**
     * 您的{0}“{1}”被合并到"{2}"，您不是合并后的销售团队成员，负责人：{3}，操作人：{4}
     */
    String SFA_CONTACT_MERGEMSGREMINDCONTENTISNOTTEAMMEMBER = "sfa.contact.mergemsgremindcontentisnotteammember";

    /**
     * 您的{0}“{1}”合并了{2}负责人：{3}，操作人：{4}
     */
    String SFA_CONTACT_MERGEMSGREMINDCONTENT = "sfa.contact.mergemsgremindcontent";
    /**
     * 业务类型为 合作伙伴员工 时不能导入客户
     */
    String SFA_CONTACT_IMPORTNOACCOUNT = "sfa.contact.importnoaccount";
    /**
     * 生日年数据不对
     */
    String SFA_CONTACT_YEAROFBIRTHEX = "sfa.contact.yearofbirthex";
    /**
     * 生日月数据不对
     */
    String SFA_CONTACT_MONTHOFBIRTHEX = "sfa.contact.monthofbirthex";
    /**
     * 生日日数据不对
     */
    String SFA_CONTACT_DAYOFBIRTHEX = "sfa.contact.dayofbirthex";
    /**
     * 业务类型不为 合作伙伴员工 时不能导入合作伙伴
     */
    String SFA_CONTACT_IMPORTNOPARTNER = "sfa.contact.importnopartner";

    /**
     * 生成签名报错
     */
    String SFA_CONTACT_GENERATINGSIGNATUREERROR = "sfa.contact.generatingsignatureerror";

    /**
     * 获取企业标识失败
     */
    String SFA_CONTACT_FAILURETOOBTAINENTERPRISEIDENTITY = "sfa.contact.failuretoobtainenterpriseidentity";

    /**
     * 使用场景错误！
     */
    String SFA_RULE_RULEOPERATIONWRONG = "sfa.rule.ruleoperationwrong";
    /**
     * 更新地址信息异常！
     */
    String SFA_ACCOUNTADDR_CHANGELOCATIONEXCEPTION = "sfa.accountaddr.changelocationexception";
    /**
     * 审核人不能为空
     */
    String SFA_CHECKER_NO_EMPTY = "sfa.checker.no_empty";
    /**
     * 成功
     */
    String SFA_SUCESSFUL = "sfa.sucessful";
    /**
     * %s 不能为空
     */
    String SFA_OBJECT_DISPLAY_NAME_NOTNULL = "sfa.object.display.name.notnull";
    /**
     * 已成交的%s不允许变更为未成交！
     */
    String SFA_ACCOUNT_NOTCHANGEWITHOUT_TRANSACTION = "sfa.account.notchangewithout.transaction";

    /**
     * 多次成交的%s不允许变更为已成交或未成交！
     */
    String SFA_ACCOUNT_MULTIPLE_TRADE_CANNOT_CHANGED = "sfa.account.multiple.trade.cannot.changed";
    /**
     * 参数错误
     */
    String SFA_PARAMET_ERERROR = "sfa.paramet.ererror";
    /**
     * 拒绝原因
     */
    String SFA_BASICSETTINGBUSINESS_6105_1 = "sfa.BasicSettingBusiness.6105.1";

    /**
     * 拒绝原因: {0} {1}
     */
    String SFA_REJECTION_REASON_NEW_RECORD = "sfa.rejection.reason.new.record";
    /**
     * 原负责人
     */
    String SFA_ORIGINAL_MANAGER = "sfa.original.manager";

    /**
     * 新负责人
     */
    String SFA_NEW_DIRECTOR = "sfa.new.director";

    /**
     * {0} 新负责人: {1}
     */
    String SFA_NEW_OWNER_NEW_RECORD = "sfa.new.owner.new.record";

    /**
     * {0} 原负责人 : {1} , 新负责人: {2}
     */
    String SFA_CHANGE_OWNER_A_B_NEW_RECORD = "sfa.change.owner.a.b.new.record";
    /**
     * {0} 原负责人 : {1} , 新负责人: {2} , 操作人:{3}
     */
    String SFA_CHANGE_OWNER_A_B_NEW_RECORD_WITH_OPERATOR = "sfa.change.owner.a.b.new.record.with.operator";
    /**
     * 无
     */
    String SFA_LOGTYPEENUM_20_1 = "sfa.LogTypeEnum.20.1";
    /**
     * 无
     */
    String SFA_NONE_VALUE = "sfa.none.value";
    /**
     * 只能领取未分配的%s
     */
    String SFA_ONLY_RECEIVE_UNASSIGNED_OBJECT = "sfa.only.receive.unassigned.object";
    /**
     * &s 成员不可见，不允许领取
     */
    String SFA_MEMBERS_NOT_VISIBLE_NOT_ALLOWED_RECEIVE = "sfa.members.not.visible.not.allowed.receive";
    /**
     * 不允许变更业务类型
     */
    String SFA_CANNOT_CHANGE_RECORDTYPE = "sfa.cannot.change.recordtype";
    /**
     * %s已经锁定不可做合并操作
     */
    String SFA_LOCKED_CANNOT_MERGE = "sfa.locked.cannot.merge";
    /**
     * 公海客户不能与非公海客户合并
     */
    String SFA_HIGH_SEAS_CANNOT_MERGE_NOHIGHSEA = "sfa.high.seas.cannot.merge.nohighsea";
    /**
     * 因公海权限不同只能合并同一公海的客户，您可先将客户转移至同一公海
     */
    String SFA_HIGHSEAS_MERGE_PERMISSION_MSG = "sfa.highseas.merge.permission.msg";
    /**
     * 禁止向公海转移未生效的客户
     * 禁止向%s转移未生效的%s
     */
    String SFA_FORBID_TRANSFERRING_EFFECTIVE = "sfa.Forbid.transferring.effective";
    /**
     * 禁止向公海转移报备中的客户
     * 禁止向%s转移报备中的%s
     */
    String SFA_FORBID_REPORTING_TRANSFERRING_EFFECTIVE = "sfa.Forbid.Reporting.transferring.effective";

    /**
     * 从 非公海 到公海
     * 从 非%s 到%s
     */
    String SFA_FROM_HS_TO_NHS = "sfa.from.hs.to.nhs";
    /**
     * 从%s
     */
    String SFA_FROM = "sfa.from";
    /**
     * 到%s
     */
    String SFA_TO = "sfa.to";
    /**
     * 请分配%s负责人
     */
    String SFA_PLEASE_ALLOCATE_OB_OWNER = "sfa.please.allocate.ob.owner";
    /**
     * 请选择销售人员退回原因
     */
    String SFA_MULTIIMPORTBUSINESS_850_1 = "sfa.MultiImportBusiness.850.1";
    /**
     * %s或%s不存在
     */
    String SFA_A_OR_B_NOT_FOUND = "sfa.a.or.b.not.found";
    /**
     * 最多支持同时处理 %s 条记录。您选择的记录数已超出，请调整。
     */
    String SFA_OVER_LIMIT_COUNT = "sfa.over.limit.count";
    /**
     * 只能分配给池成员
     */
    String SFA_ONLY_ALLOCATE_POOL_MEMBER = "sfa.only.allocate.pool.member";
    /**
     * 员工ID参数错误
     */
    String SFA_TRADEPRODUCTBUSINESS_2077_1 = "sfa.TradeProductBusiness.2077.1";
    /**
     * 分配人为空
     */
    String SFA_ALLOCATION_EMPTY = "sfa.allocation.empty";

    String SFA_NOT_OBJECT_MEMBER_CANT_ALLOCATE = "sfa.not.object.member.cant.allocate";
    /**
     * 超过线索领取上限，不能分配%s
     */
    String SFA_OVER_ALLOCATE_LIMIT = "sfa.over.allocate.limit";
    /**
     * %s不是%s %s成员，不能分配%s
     */
    String SFA_NOT_POOLOBJECT_CANT_REMOVE = "sfa.not.poolobject.cant.remove";
    /**
     * 销售线索 %s 已转换，无法分配
     * %s %s 已转换，无法分配
     */
    String SFA_LEADS_CHANGE_CANT_ALLOCATE = "sfa.leads.change.cant.allocate";

    /**
     * %s 新负责人超过线索领取上限，无法更换负责人
     */
    String SFA_LEADS_NEW_OWNER_CHOOSE_LIMIT_ERR = "sfa.leads.new.owner.choose.limit.error";

    /**
     * %s 新负责人不是线索所在线索池成员，无法更换负责人
     */
    String SFA_NEW_OWNER_NOT_POOL_MEMBER = "sfa.new.owner.not.pool.member";
    /**
     * 销售线索[%s] 已转换，无法添加到活动成员
     * %s[%s] 已转换，无法添加到 %s
     */
    String SFA_LEADS_CHANGE_CANT_ADDTOCAMPAIGNMEMBERS = "sfa.leads.change.cant.addtocampaignmembers";
    /**
     * 销售线索 %s 已作废，无法分配
     * %s %s 已作废，无法分配
     */
    String SFA_LEADS_INVALID_CANT_ALLOCATE = "sfa.leads.invalid.cant.allocate";
    /**
     * 线索 %s %s  给 %s  线索池 %s
     * %s %s %s 给 %s %s %s
     */
    String SFA_LEADS_TO_LEADSPOOL_MSG = "sfa.leads.to.leadspool.msg";

    /**
     * {线索0} {1} {2} 给 {3}，{线索池4} {5}
     */
    String SFA_LEADS_TO_LEADSPOOL_MSG_NEW = "sfa.leads.to.leadspool.msg.new";

    /**
     * 自动分配 销售线索 %s 在%s时自动分配负责人为 %s。所属%s为 %s
     */
    String SFA_AUTO_ALLOCATE_LOG_MSG = "sfa.leads.auto_allocate_log_msg";

    /**
     * 自动分配 {销售线索0} {1} 在 {2} 时自动分配负责人为 {3}。所属 {线索池4} 为 {5}
     */
    String SFA_AUTO_ALLOCATE_LOG_MSG_NEW = "sfa.leads.auto.allocate.log.msg.new";

    /**
     * 销售线索 %s 不在线索池，不能领取
     * %s %s 不在 %s,不能领取
     */
    String SFA_LEADS_NOT_IN_LEADSPOOL = "sfa.leads.not.in.leadspool";
    /**
     * 超过线索领取上限，不能领取 %s
     * 超过%s领取上限，不能领取 %s
     */
    String SFA_OVER_CHOOSE_LIMIT = "sfa.over.choose.limit";

    /**
     * 线索：【%s】的状态不是未分配，不允许领取
     * %s：【%s】的状态不是未分配，不允许领取
     */
    String SFA_CHOOSE_MUST_BE_UN_ASSIGNED = "sfa.choose.must.be.un_assigned";
    /**
     * 您不是线索【 %s 】所在线索池的成员，不允许领取
     * 您不是%s【 %s 】所在%s的成员，不允许领取
     */
    String SFA_CHOOSE_MUST_IN_LEADSPOOL = "sfa.choose.must.in.leadspool";

    /**
     * %s 中的 %s 已被领取，领取人：%s
     */
    String SFA_HAS_BEEN_CHOOSED_MSG = "sfa.has.been.choosed.msg";

    /**
     * {0} 中的 {1} 已被领取，领取人：{2}
     */
    String SFA_HAS_BEEN_CHOOSED_MSG_NEW_RECORD = "sfa.has.been.choosed.msg.new.record";

    /**
     * %s 已被删除
     */
    String SFA_HAS_BEEN_DELETED = "sfa.has.been.deleted";
    /**
     * 该线索不能执行此操作
     * 该%s不能执行此操作
     */
    String SFA_OBJECT_CANT_DO_THIS_JOB = "sfa.object.cant.do.this.job";
    /**
     * 处理结果不能为空
     */
    String SFA_PROCESS_RST_CANT_BE_NULL = "sfa.process.rst.cant.be.null";

    /**
     * 无效原因不能为空
     */
    String SFA_INVALID_REASON_CANT_BE_EMPTY = "sfa.invalid.reason.cant.be.empty";

    /**
     * 跟进中 线索 %s
     * 跟进中 %s %s
     */
    String SFA_LEADS_FLLOWING_UP = "sfa.leads.fllowing.up";

    /**
     * 跟进中 {线索0} {1}
     */
    String SFA_LEADS_FLLOWING_UP_NEW = "sfa.leads.fllowing.up.new";

    /**
     * 无效 线索 %s ，无效原因 %s
     * 无效 %s %s ，无效原因 %s
     */
    String SFA_INVALID_REASON_MSG = "sfa.invalid.reason.msg";

    /**
     * 无效 {线索0} {1}，无效原因：{2}
     */
    String SFA_INVALID_REASON_MSG_NEW = "sfa.invalid.reason.msg.new";

    /**
     * 此线索负责人不是线索池成员
     * 此%s负责人不是%s成员
     */
    String SFA_LEADS_OWNER_NOT_IN_LEADS_POOL = "sfa.leads.owner.not.in.leads.pool";

    /**
     * 此线索在系统中存在多个
     * 此%s在系统中存在多个
     */
    String SFA_EXITS_MULTIPLE_OBJECT = "sfa.exits.multiple.object";
    /**
     * 不能将已转换的销售线索合并为其它状态的销售线索
     * 不能将已转换的%s合并为其它状态的%s
     */
    String SFA_CANT_MERGE_VOVERTED_LEADS = "sfa.cant.merge.converted.leads";

    /**
     * 不能将已转换的销售线索合并为其它状态的销售线索
     * 不能将已转换的{0}合并为其它状态的{1}
     */
    String SFA_CANT_MERGE_VOVERTED_LEADS_NEW = "sfa.cant.merge.converted.leads.new";

    /**
     * 不能将状态正常的销售线索合并为已作废的销售线索
     * 不能将状态正常的%s合并为已作废的%s
     */
    String SFA_CANT_MERGE_INV_LEADS = "sfa.cant.merge.inv.leads";

    /**
     * 不能将状态正常的线索合并为已作废的线索
     * 不能将状态正常的{0}合并为已作废的{1}
     */
    String SFA_CANT_MERGE_INV_LEADS_NEW = "sfa.cant.merge.inv.leads.new";

    /**
     * 只有线索池相同的线索，才能相互合并。请先将所选线索移至同一线索池下（或移出线索池），再尝试合并操作。
     * 只有%s相同的%s，才能相互合并。请先将所选%s移至同一%s下（或移出%s），再尝试合并操作。
     */
    String SFA_CANT_MERGE_POOL_LEADS = "sfa.cant.merge.pool.leads";
    /**
     * 超过线索领取上限，不能转移 %s
     * 超过%s领取上限，不能转移 %s
     */
    String SFA_OVER_MOVE_LIMIT = "sfa.over.move.limit";

    /**
     * 线索 %s %s，原线索池 %s，目标线索池 %s
     * %s %s %s，原%s %s，目标%s %s
     */
    String SFA_POOL_MOVE_A_TO_B_MSG = "sfa.pool.move.a.to.b.msg";

    /**
     * 线索 {0} {1}， 源线索池 {2}，目标线索池 {3}
     */
    String SFA_POOL_MOVE_A_TO_B_MSG_NEW = "sfa.pool.move.a.to.b.msg.new";

    /**
     * ，源线索池 {0}，目标线索池 {1}
     */
    String SFA_POOL_MOVE_A_TO_B_POOL_MSG = "sfa.pool.move.a.to.b.pool.msg";

    /**
     * 无法操作
     */
    String SFA_UNABEL_OPERATE = "sfa.unabel.operate";
    /**
     * 已作废
     */
    String SFA_HAS_BEEN_INVALID = "sfa.has.been.invalid";
    /**
     * %S已作废
     */
    String SFA_OBJ_HAS_BEEN_INVALID = "sfa.obj.has.invalid";

    /**
     * 线索状态未进行处理
     * %s状态未进行处理:SalesClueID=%s, SalesClueStatusType=%s
     */
    String SFA_OBJECT_STATUS_UNPROCESSED = "sfa.object.status.unprocessed";

    /**
     * 线索 线索名
     * {0} {1}
     */
    String SFA_LEADS_POOL_LOG = "sfa.leads.pool.log";

    /**
     * 线索 %s %s，线索池 %s，销售人员退回原因 %s
     * %s %s %s，%s %s，销售人员退回原因 %s
     */
    String SFA_LEADS_RETURN_REASON_LOG = "sfa.leads.return.reason.log";

    /**
     * {线索0} {1} {2}，退回到{线索池3} {4}，销售人员退回原因: {5}
     */
    String SFA_LEADS_RETURN_REASON_LOG_NEW = "sfa.leads.return.reason.log.new";

    /**
     * ，原负责人 {0}，线索池 {1}
     */
    String SFA_LEADS_RETURN_POOL_LOG = "sfa.leads.return.pool.log";

    /**
     * 同一销售流程,不可变更阶段
     */
    String SFA_SAME_SALES_PROCESS_ERROR_MSG = "sfa.same.sales.process.error.msg";
    /**
     * 当前的销售流程中不包含当前阶段
     */
    String SFA_CURRENT_STAGENOT_INCLUED_SALES_PROCESS_ERROR_MSG = "sfa.current.stagenot.inclued.sales.process.error.msg";
    /**
     * 不可跳转到该阶段
     */
    String SFA_CANT_JUMP_THIS_STAGE = "sfa.cant.jump.this.stage";

    /**
     * 当前阶段已是终结态不可跳转
     */
    String SFA_CURRENT_FINAL_STATE_ERROR_MSG = "sfa.current.final.state.error.msg";

    /**
     * 要跳转的销售流程中不包含当前阶段
     */
    String SFA_NOT_INCLUDED_SALES_PROCESS = "sfa.not.included.sales.process";
    /**
     * 销售流程不能为空
     */
    String SFA_SALES_PROCESS_CANNOT_BE_EMPTY = "sfa.sales.process.cannot.be.empty";
    /**
     * 销售流程不存在
     */
    String SFA_SALES_PROCESS_DOES_NOT_EXIST = "sfa.sales.process.does.not.exist";
    /**
     * 流程不适用所在部门
     */
    String SFA_PROCESS_DOESNOT_APPLY_DEPARTMENT = "sfa.process.doesnot.apply.department";

    /**
     * 请稍后重试
     */
    String SFA_TRY_AGAIN = "sfa.try.again";
    /**
     * 数据已被修改，请刷新页面
     */
    String SFA_DATA_MODIFIED_REFRESH_PAGE = "sfa.data.modified.refresh.page";
    /**
     * 不是公海成员,场景错误
     */
    String SFA_NOT_IN_HIGHSEAS_SEARCH_TEMP_ERROR = "sfa.not.in.highseas.search.temp.error";
    /**
     * 不能为0
     */
    String SFA_CANT_BE_ZERO = "sfa.cant.be.zero";


    /**
     * 不存在%s
     */
    String SFA_NOT_EXISTS = "sfa.not.exists";
    /**
     * 已存在%s
     */
    String SFA_EXISTS = "sfa.exists";

    /**
     * 产品存在重复
     */
    String PRODUCT_DUPLICATE_NAME = "sfa.product.duplicate";
    /**
     * %s%s不匹配
     */
    String SFA_NOT_MATCH = "sfa.not.match";
    /**
     * 产品 %s 不在当前价目表范围内
     */
    String SFA_PRODUCT_NOT_IN_PRICE_LIST = "sfa.product.not.in.price.list";
    /**
     * 产品 %s 不在当前销售合同内
     */
    String SFA_PRODUCT_NOT_IN_SALE_CONTRACT = "sfa_product_not_in_sale_contract";
    /**
     * 价目表产品不在当前价目表范围内
     */
    String SFA_PRICE_BOOK_PRODUCT_OVER_RANGE = "sfa.price.book.product.over.range";

    /**
     * 不能修改为此退款方式
     */
    String SFA_CANNOT_MODIFY_REFUND_METHOD = "sfa.cannot.modify.refund.method";
    /**
     * 商机ID不存在
     */
    String SFA_OPPORTUNITY_ID_NOT_EXISTS = "sfa.opportunity.id.not.exists";
    /**
     * 产品在当前商机中已经存在
     */
    String SFA_PRODUCT_ALREADY_EXIST_THIS_OPPORTUNITY = "sfa.product.already.exist.this.opportunity";
    /**
     * 校验当前价目表是否适用当前客户
     */
    String SFA_VERIFY_CURRENT_PRICE_BOOK_APPLICABLE_ACCOUNT = "sfa.verify.current.price.book.applicable.account";
    /**
     * 价目表不适用当前客户
     */
    String SFA_PRICE_LIST_NOT_APPLY_CURRENT_CUSTOMERS = "sfa.price.list.not.apply.current.customers";
    /**
     * 产品在当前商机2.0中已经存在
     */
    String SFA_PRODUCT_ALREADY_EXIST_THIS_NEWOPPORTUNITY = "sfa.product.already.exist.this.newopportunity";
    /**
     * 不是线索池成员,场景错误
     */
    String SFA_NOT_POOL_MEMBER_SEARCH_TEMP_ERROR = "sfa.not.pool.member.search.temp.error";

    /**
     * 不是公海管理员,场景错误
     */
    String SFA_NOT_HIGHSEAS_ADMINISTRATOR_SEARCH_TEMP_ERROR = "sfa.not.highseas.administrator.search.temp.error";

    /**
     * 导入时，退货单_产品主键是退货单的唯一标识，并用于关联退货单产品，请手动录入。
     * 导入时，%s主键是%s的唯一标识，并用于关联%s，请手动录入。
     */
    String SFA_RETURNEDGOODSINVOICEOBJECTIMPORTPROVIDER_TIP = "sfa.returnedGoodsInvoiceObjectImportProvider.tip";

    /**
     * %s保有量上限
     */
    String SFA_OBJECT_RETENTION_LIMIT = "sfa.object.retention.limit";

    /**
     * 有新功能上线啦，请先升级客户端!
     */
    String SFA_COMMONUTIL_257_2 = "sfa.CommonUtil.257.2";
    /**
     * 系统错误，请稍候重试!
     */
    String SFA_SYS_ERROR_MSG = "sfa.sys.error.msg";
    /**
     * 数据被他人操作，请稍候重试!
     */
    String SFA_DATA_IN_OPERATION_ERROR = "sfa.data.in.operation.error";

    /**
     * 地址参数格式错误!
     */
    String SFA_BASICSETTINGBUSINESS_3052_1 = "sfa.BasicSettingBusiness.3052.1";

    /**
     * 行业参数格式错误!
     */
    String SFA_BASICSETTINGBUSINESS_3070_1 = "sfa.BasicSettingBusiness.3070.1";
    /**
     * 缺少必要参数
     */
    String SFA_MISS_REQUIRED_PARAMETERS = "sfa.miss.required.parameters";

    /**
     * 缺少必要参数:{0}
     */
    String SFA_MISS_REQUIRED_PARAMETERS_SHOW_PARAM = "sfa.miss.required.parameters.show.param";

    /**
     * 母件产品不能为空
     */
    String SFA_NOT_FIND_BOM_CORE_PRODUCT_ID_ERROR = "sfa.not.find.bom.core.product.id.error";
    /**
     * 没找到产品组合，请检查当前产品是否配置过产品组合
     */
    String SFA_NOT_FIND_BOM_CORE_ERROR = "sfa.not.find.bom.core.error";

    /**
     * 当前产品找到多个产品组合，在当前传参格式下，请确保当前产品只有一个产品组合
     */
    String SFA_MULTI_FIND_BOM_CORE_ERROR = "sfa.multi.find.bom.core.error";
    /**
     * 请勿修改业务类型
     */
    String SFA_MARKETINGEVENTBUSINESS_1472_1 = "sfa.MarketingEventBusiness.1472.1";
    /**
     * 无权限领取%s或%s
     */
    String SFA_NO_PERMISSION_RECEIVE_A_B = "sfa.no.permission.receive.a.b";
    /**
     * 已经开启了报备，请选择审核人！
     */
    String SFA_BASICSETTINGBUSINESS_3034_1 = "sfa.BasicSettingBusiness.3034.1";
    /**
     * 目标%s不能与来源%s重复
     */
    String SFA_TARGET_NOT_DUP_WITH_SOURCE = "sfa.target.not.dup.with.source";
    /**
     * %s编号重复!
     */
    String SFA_REPEAT_NUMBER = "sfa.repeat.number";
    /**
     * 达到%s保有量上限!
     */
    String SFA_REACH_LIMIT_OBJ = "sfa.reach.limit.obj";

    /**
     * 达到%s保有量上限!
     */
    String SFA_REACH_POOL_LIMIT_OBJ = "sfa.reach.pool.limit.obj";

    /**
     * 抱歉，由于【s%】保有量规则控制，达到%s保有量上限!
     */
    String SFA_REACH_RULE_LIMIT_OBJ = "sfa.reach.rule.limit.obj";

    /**
     * 更新导入负责人不允许为空！
     */
    String SFA_UPDATE_IMPORT_OWNER_NOT_NULL = "sfa.update.import.owner.not.null";

    /**
     * 新建公海客户时不支持同时分配客户，请移除负责人字段再重试!
     * 新建%s %s时不支持同时分配%s，请移除负责人字段再重试!
     */
    String SFA_NEW_HIGHSEAS_ACCOUNT_CANT_RM_ACCOUNT = "sfa.new.highseas.account.cant.rm.account";

    /**
     * %s天内不能连续领取同一个%s
     */
    String SFA_CANT_RECEIVE_SAME_IN_DAYS = "sfa.cant.receive.same.in.days";
    /**
     * 没有权限操作跟进成交动作
     */
    String SFA_NO_PERMISSION_FLOW_UP = "sfa.no.permission.flow.up";
    /**
     * %s更换负责人、分配、领取不允许取消跟进行为
     */
    String SFA_OBJ_REP_CANT_CANCEL_FLOW_UP = "sfa.obj.rep.cant.cancel.flow.up";
    /**
     * %s成交行为设置错误
     */
    String SFA_TRANSACTION_SETTING_ERROR = "sfa.transaction.setting.error";
    /**
     * 更新失败
     */
    String SFA_UPDATE_FAILURE = "sfa.update.failure";
    /**
     * %s转换:%s对象不能为空
     */
    String SFA_CONVERSION_OBJECT_NOT_EMPTY = "sfa.conversion.object .not.empty";
    /**
     * %s转换:%s对象不存在
     */
    String SFA_CONVERSION_OBJECT_NOT_EXISTS = "sfa.conversion.object.not.exists";

    /**
     * 已转换的线索处于锁定状态时不能作废
     */
    String SFA_LEADS_INVALID_TRANSFORMED_LOCKED_INFO = "sfa.leads.invalid.transformed.locked.info";

    /**
     * 已转换的%s不能再次转换
     */
    String SFA_CONVERSION_LEADS_HAS_TRANSFORMED = "sfa.conversion.leads.has.transformed";
    /**
     * %s必须转换为
     */
    String SFA_OBJ_MUST_CONVERTED_TO = "sfa.obj.must.converted.to";

    /**
     * %s不允许转换为
     */
    String SFA_TRANSFER_CONFIG_CHECK_ALLOW_FAIL = "sfa.transfer.config.check.allow.fail";

    /**
     * 当前操作人没有 %s 转换新建功能权限。
     */
    String SFA_CURRENT_USER_NO_TRANSFER_ADD_PRIVILEGE = "sfa.current.user.no.transfer.add.privilege";

    /**
     * %s不能同时转换为%s和%s
     */
    String SFA_CANT_CONVERTED_TO_A_B = "sfa.cant.converted.to.a.b";
    /**
     * 暂未开启%s，无法进行转换
     */
    String SFA_OBJECT_ENABLED_CONVERSION_POSSIBLE = "sfa.object.enabled.conversion.possible";
    /**
     * 标准价目表不适合当前%s
     */
    String SFA_STANDARD_PRICE_NOT_SUT_OBJ = "sfa.standard.price.not.sut.obj";
    /**
     * 查重有重复数据
     */
    String SFA_CHECK_HAS_DUPLICATE_DATA = "sfa.check.has.duplicate.data";
    /**
     * 销售记录合并失败
     */
    String SFA_CONTRACTBUSINESS_366_1 = "sfa.ContractBusiness.366.1";
    /**
     * 系统不允许关联已有%s，请新建一个%s！
     */
    String SFA_HAS_RELATION_PLEASE_NEW_ONE = "sfa.has.relation.please.new.one";
    /**
     * 线索 %s ， 转换为 客户 %s %s
     * %s %s ， 转换为 %s %s %s
     */
    String SFA_CONVERT_TO_A_B_C = "sfa.convert.to.a.b.c";

    /**
     * {线索} {xx}，转换为 {客户} {xx}{，}{联系人} {xx}{，}{商机} {xx}{，}{新商机} {xx}勾选了......
     * 13-34 勾选了...
     * {0} {1}，转换为 {2} {3}{4}{5} {6}{7}{8} {9}{10}{11} {12}{13}{14}{15}{16}{17}{18}{19}{20}{21}{22}{23}{24}{25}{26}{27}{28}{29}{30}{31}{32}{33}{34}。
     */
    String SFA_LEADS_TRANSFER_MODIFY_THE_RECORD = "sfa.leads.transfer.modify.the.record";
    /**
     * 勾选了相关团队自动带入
     */
    String SFA_LEADS_TRANSFER_SELECT_RELEVANT_TEAM_NEW = "sfa.leads.transfer.select.bringin.relevant.team.new";
    /**
     * 勾选了销售记录自动带入
     */
    String SFA_LEADS_TRANSFER_SELECT_SALES_RECORD_NEW = "sfa.leads.transfer.select.bringin.sales.record.new";

    /**
     * 勾选了[销售线索的相关团队自动带入客户、联系人、新商机]
     */
    String SFA_LEADS_TRANSFER_SELECT_RELEVANT_TEAM = "sfa.leads.transfer.select.bringin.relevant.team";

    /**
     * 勾选了[销售线索的销售记录自动带入客户、联系人、新商机]
     */
    String SFA_LEADS_TRANSFER_SELECT_SALES_RECORD = "sfa.leads.transfer.select.bringin.sales.record";
    /**
     * 无此客户查看权限，不能进行转换
     */
    String SFA_TRANSFER_PROHIBIT = "sfa.transfer.prohibit";
    String SFA_SYSTEM_NAME = "sfa.Consts.115.1";
    String SFA_TRANSFER_ADD_MEMBER = "sfa.transfer.add.member";
    String SFA_TRANSFER_ADD_MEMBER_PARTNER = "sfa.transfer.add.member.partner";
    /**
     * 姓名：[未知]
     */
    String SFA_LEADS_IMPORT_NOTICE_A = "sfa.TradeRefundBusiness.243.1";

    /**
     * 姓名：%s
     */
    String SFA_LEADS_IMPORT_NOTICE_B = "sfa.TradeRefundBusiness.254.1";


    /**
     * % 给您分配了线索：%
     */
    String SFA_LEADS_ALLOCATE_NOTICE = "sfa.leads.allocate.notice";

    /**
     * {0} 给您分配了 {1}：{2}
     */
    String SFA_LEADS_ALLOCATE_NOTICE_NEW_RECORD = "sfa.leads.allocate.notice.new.record";

    /**
     * 转移的线索没有线索池
     */
    String SFA_NO_LEADS_POOL_SOURCE = "sfa.leadspool.no.source";

    /**
     * 不能保存未转换的线索
     */
    String SFA_LEADS_CAN_NOT_SAVE_NO_TRANSFORMED = "sfa.leads.cannot.save.no.transformed";

    /**
     * 不能保存已转换的线索
     */
    String SFA_LEADS_CAN_NOT_SAVE_TRANSFORMED = "sfa.leads.cannot.save.transformed";

    /**
     * 此%s还在锁定期，您可在%s天后再领取！
     */
    String SFA_LEADS_CANT_RECEIVE_SINGLE_SAME_IN_DAYS = "sfa.leads.cant.receive.single.same.in.days";
    /**
     * 受领取规则限定，有%s条%s仍在锁定期，未能领取成功！
     */
    String SFA_LEADS_CANT_RECEIVE_SAME_IN_DAYS = "sfa.leads.cant.receive.same.in.days";
    /**
     * 插入数据失败
     */
    String SFA_ACCOUNT_INSERTDATAERROR = "sfa.account.insertdataerror";
    /**
     * 删除数据失败
     */
    String SFA_ACCOUNT_DELETEDATAERROR = "sfa.account.deletedataerror";
    /**
     * %s与%s不匹配！
     */
    String SFA_A_NOT_MATCH_B = "sfa.a.not.match.b";
    String ACCOUNT_OBJ_KEY = "AccountObj.attribute.self.display_name";
    String PRODUCT_OBJ_KEY = "ProductObj.attribute.self.display_name";
    String LEADS_OBJ_KEY = "LeadsObj.attribute.self.display_name";
    String LEADS_POOL_OBJ_KEY = "LeadsPoolObj.attribute.self.display_name";
    String SALES_ORDER_OBJ_KEY = "SalesOrderObj.attribute.self.display_name";
    String NEWOPPORTUNITY_OBJ_KEY = "NewOpportunityObj.attribute.self.display_name";
    String HIGHSEAS_OBJ_KEY = "HighSeasObj.attribute.self.display_name";
    String CONTACK_OBJ_KEY = "ContactObj.attribute.self.display_name";
    String SFA_GIVE_WORD = "sfa.give.word";

    /**
     * ，给 {0} ，{线索池1} {2}
     */
    String SFA_LEADS_ALLOCATION_GIVE = "sfa.leads.allocation.give";

    String SFA_LEADS_DUPLICATED_UPDATE_STATUS_ERROR = "sfa.leads.duplicated.update.status.error";
    //规则不能%s，原因如下：当前规则已被【重复线索处理】规则引用
    String SFA_LEADS_DUPLICATED_RULE_REFERENCED_ERROR = "sfa.leads.duplicated.rule.referenced.error";
    /**
     * 替换相关数据失败！
     */
    String SFA_NEWOPPORTUNITYCONTACT_REPLACERELATEDDATAFAILES = "sfa.newopportunitycontact.replacerelateddatafailes";

    /**
     * 当前图谱中存在游离的节点！
     */
    String SFA_NEWOPPORTUNITYCONTACT_EXISTFREEOFNODES = "sfa.newopportunitycontact.existfreeofnodes";

    /**
     * %s超限规则设置有误，请正确设置!
     */
    String SFA_OBJECT_LIMIT_OVER_RULE_ERROR = "sfa.object.limit.over.rule.error";

    /**
     * 请注意，您的%s保有量已达上限，通过第三方系统对接来的数据将不再分配给您，直接存入%s，如有疑问可咨询您的CRM管理员。
     */
    String SFA_OBJECT_LIMIT_OVER_RULE_NOTICE = "sfa.object.limit.over.rule.notice";

    /**
     * 请注意，您的{0}保有量已达上限，通过第三方系统对接来的数据将不再分配给您，直接存入{1}，如有疑问可咨询您的CRM管理员。
     */
    String SFA_OBJECT_LIMIT_OVER_RULE_NOTICE_FOR_NEW_RECORD = "sfa.object.limit.over.rule.notice.new.record";

    /**
     * 达到保有量上限
     */
    String SFA_OBJECT_LIMIT_OVER_RULE_NOTICE_TITLE = "sfa.object.limit.over.rule.notice.title";

    String SFA_DATA_CLEAN_CLEANING_ERROR_MSG = "sfa.data.clean.cleaning.error.msg";

    /**
     * %s %s 到 %s
     */
    String SFA_LEADS_COLLECTED_TO = "sfa.leads.collected_to.log";
    /**
     * 自动归集 ，%s %s 到 %s
     */
    String SFA_LEADS_AUTO_COLLECTED_TO = "sfa.leads.auto.collected_to.log";

    /**
     * 无适用公海
     */
    String SFA_HIGH_SEAS_NO_DATA = "sfa.high.seas.no.data";
    /**
     * 无适用线索池
     */
    String SFA_LEADS_POOL_NO_DATA = "sfa.leads.pool.no.data";

    /**
     * 该公海只允许本公海成员转移到客户到该公海
     */
    String SFA_HIGH_SEAS_ONLY_ALLOW_MOVE = "sfa.high.seas.only.allow.move";

    /**
     * 公海客户不支持新建导入触发审批流程
     */
    String SFA_HIGH_SEAS_NOT_SUPPORT_APPROVALFLOW = "sfa.high.seas.not.support.approvalflow";

    /**
     * 该线索池只允许本线索池成员转移线索到该线索池
     */
    String SFA_LEADS_POOL_ONLY_ALLOW_MOVE = "sfa.leads.pool.only.allow.move";
    /**
     * 不能编辑,因:当前开票创建时间为开启订单产品模式之前
     */
    String SFA_INVOICE_CANT_EDIT_REASON_1 = "sfa.invoice.cant.edit.reason.1";

    /**
     * 客户与销售订单不匹配
     */
    String SFA_ACCOUNT_ORDER_NOT_MATCH = "sfa.account.order.not.math";
    /**
     * 销售订单 %s 开票金额大于可开票金额
     */
    String SFA_SALES_ORDER_INVOICE_OVER_FLOW = "sfa.sales.order.invoice.over.flow";
    /**
     * 开票金额不能为空
     */
    String SFA_INVOICE_AMOUNT_CAMT_NULL = "sfa.invoice.amount.cant.null";
    /**
     * 开票金额必须大于0
     */
    String SFA_INVOICE_AMOUNT_NEED_GT_ZERO = "sfa.invoice.amount.need.gt.zero";
    /**
     * 开票明细金额必须>0
     */
    String SFA_INVOICE_LINE_AMOUNT_GT_ZERO = "sfa.invoice.line.amount.need.gt.zero";

    /**
     * 该客户已建立客户层级关系不支持作废
     */
    String SFA_ACCOUNT_NOTALLOWINVALID = "sfa.account.notallowinvalid";

    /**
     * 客户树关系存在上级或下级关系，不允许作废
     */
    String SFA_ACCOUNT_TREE_RELATION_NOTALLOWINVALID = "sfa.account.tree.relation.not.allowed.invalid";

    /**
     * 该%s已开启互联业务，无法作废。如需作废，请停用该%s关联的互联企业
     */
    String SFA_RELATIONENTERPRISE_NOTALLOWINVALID = "sfa.relationenterprise.notallowinvalid";

    /**
     * 该联系人已建立联系人关系不支持作废
     */
    String SFA_CONTACT_NOTALLOWINVALID = "sfa.contact.notallowinvalid";

    /**
     * 所选客户已是该客户的上级客户或子级客户
     */
    String SFA_ACCOUNT_CHILDISFATHER = "sfa.account.childisfather";

    /**
     * 未生效状态对象不允许合并
     */
    String SFA_ACCOUNT_INEFFECTIVECANNOTBEMERGED = "sfa.account.ineffectivecannotbemerged";

    /**
     * 不能将状态正常的对象合并为已作废对象
     */
    String SFA_ACCOUNT_INVALIDCANNOTBEMERGED = "sfa.account.invalidcannotbemerged";

    /**
     * 对象关联了合作伙伴不能进行合并操作
     */
    String SFA_ACCOUNT_LOOKUPPARTNERCANNOTBEMERGED = "sfa.account.lookuppartnercannotbemerged";

    /**
     * 只有池成员才能转移对象到该池
     */
    String SFA_POOL_POOLTOPOOLCHECK = "sfa.pool.pooltopoolcheck";

    /**
     * 使用系统预置成交规则不允许手动更改客户成交状态
     */
    String SFA_ACCOUNT_STATUSCHANGE = "sfa.account.statuschange";

    /**
     * 数据发生变更，请刷新重试
     */
    String SFA_ACCOUNT_DATAISCHANGE = "sfa.account.dataischange";

    /**
     * 保存引用关系失败
     */
    String SFA_SAVE_REFERENCE_FAILED = "sfa.save.reference.failed";

    /**
     * 客户层级不能超过10级
     */
    String SFA_ACCOUNT_LEVELCANNOTEXCEEDTEN = "sfa.account.levelcannotexceedten";

    /**
     * 正在进行数据刷新，请勿重复修改
     */
    String SFA_BE_IN_PROGRESS_REFRESH = "sfa.be.in.progress.refresh";

    /**
     * 处理规则已到上限，不能继续新建、复制
     */
    String SFA_OPERATION_REACH_CELLING = "sfa.operation.reach.celling";


    /**
     * {0} 新外部负责人超过线索领取上限，无法更换外部负责人
     */
    String SFA_OVER_ALLOCATE_LIMIT_NOT_CHANGE = "sfa.over.allocate.limit.not.change";

    /**
     * 数据不存在
     */
    String DATA_IS_NULL = "sfa.data.is.not.find";

    /**
     * 无法根据导入文件匹配到 CRM 中的对象数据
     */
    String EXCEL_DATA_MATCH_ERROR = "excel.data.match.error";

    /**
     * 导入表格中包含系统中不存在的分类数据，请调整后重新导入。
     */
    String EXCEL_EXISTS_DATA_IS_NULL = "excel.exists.data.is.null";

    /**
     * 客户层级不能超过{0}级
     */
    String SFA_ACCOUNT_LEVELCANNOTEXCEED = "sfa.account.levelcannotexceed";

    /**
     * 无可用的产品
     */
    String SFA_NO_PRODUCTS_AVAILABEL = "sfa.product.NoProductsAvailable";
    /**
     * 没有负责人
     */
    String SFA_SALEACTIONSETTINGBUSINESS_859_1 = "sfa.SaleActionSettingBusiness.859.1";
    /**
     * 对象不存在或已被删除！
     */
    String SFA_INVOICEBUSINESS_329_1 = "sfa.InvoiceBusiness.329.1";

    /**
     * 订单产品id为空
     */
    String SFA_ORDER_PRODUCT_ID_IS_EMPTY = "sfa.order.product.id.is.empty";

    /**
     * 查询不到销售订单产品,或是无权限
     */
    String SFA_CANT_FIND_ORDER_PRODUCT_OR_NOT_PERMISSION = "sfa.cant.find.order.product.or.not.permission";

    /**
     * 订单产品与订单不匹配
     */
    String SFA_PRODUCT_ORDER_NOT_MATCH = "sfa.product.order.not.match";
    /**
     * 订单产品中的产品与开票明细的产品不匹配。
     */
    String SFA_PRODUCT_NOT_MATCH = "sfa.invoice.product.not.match";

    /**
     * 订单产品与订单不匹配
     */
    String SFA_PRODUCT_ORDER_PRODUCT_NOT_MATCH = "sfa.product.order.product.not.match";

    /**
     * 上级客户不存在，请先导入上级客户后再导入下级客户。
     */
    String SFA_ACCOUNT_PARENTISNOTEXIST = "sfa.account.parentisnotexist";

    /**
     * 图谱中节点不能超过1000个！
     */
    String SFA_CONTACT_GRAPHNODESCOUNTLIMIT = "sfa.contact.graphnodescountlimit";

    /**
     * 数据查询失败，请刷新重试
     */
    String SFA_DATA_QUERYFAILED = "sfa.data.queryfailed";

    /**
     * 该{0}已在{1}下配置了联系人关系,确定继续作废吗？作废将解除关系。
     */
    String SFA_CONTACT_BULKINVALIDINFOFROATLAS = "sfa.contact.bulkinvalidinfofroatlas";

    /**
     * 联系人已在客户下建立【上级】联系人关系，【上级】联系人关系仅同一客户下才可建立，故不能合并
     * {0}已在{1}下建立【上级】联系人关系，【上级】联系人关系仅同一{2}下才可建立，故不能合并。
     */
    String SFA_CONTACT_NOTONEACCOUNT = "sfa.contact.notoneaccount";

    /**
     * 【上级】联系人关系不可以成环。
     * 【上级】联系人关系不可以成环。
     */
    String SFA_CONTACT_NOTALLOWHASCYCLE = "sfa.contact.notallowhascycle";

    /**
     * 编辑 {0} 的联系人关系
     */
    String SFA_CONTACT_EDITCONTACTATLAS = "sfa.contact.editcontactatlas";

    /**
     * 新增与{0}的{1}关系
     */
    String SFA_CONTACT_ADDCONTACTATLAS = "sfa.contact.addcontactatlas";

    /**
     * 修改与{0}的{1}关系为{2}关系
     */
    String SFA_CONTACT_UPDATECONTACTATLAS = "sfa.contact.updatecontactatlas";

    /**
     * 删除与{0}的{1}关系
     */
    String SFA_CONTACT_DELETECONTACTATLAS = "sfa.contact.deletecontactatlas";

    /**
     * 将{0} {1} 的所有数据转移到了 {2} {3}
     */
    String SFA_CONFIG_POOLTOPOOL = "sfa.config.pooltopool";

    /**
     * 因系统异常合并失败
     */
    String SFA_ACCOUNT_MERGEFAILEDDUETOSYSTEMEXCEPTION = "sfa.account.mergefailedduetosystemexception";

    /**
     * 合并失败
     */
    String SFA_MERGE_MERGEFAILED = "sfa.merge.mergefailed";

    /**
     * 合并数据：{0},{1} 失败,
     */
    String SFA_ACCOUNT_MERGEFAILED = "sfa.account.mergefailed";

    /**
     * 合并{0}成功
     */
    String SFA_ACCOUNT_MERGESUCCESSED = "sfa.account.mergesuccessed";

    /**
     * 合并{0}错误
     */
    String SFA_ACCOUNT_MERGEWARN = "sfa.account.mergewarn";
    /**
     * 无权限
     */
    String SFA_NO_PERMISSION = "sfa.no_permission";

    /**
     * 从{0}添加
     */
    String SFA_UDOBJ_ACTION_SAVEFROMOBJECT = "sfa.udobj.action.savefromobject";

    /**
     * 添加到{0}
     */
    String SFA_UDOBJ_ACTION_SAVEASOBJECT = "sfa.udobj.action.saveasobject";

    /**
     * 添加
     */
    String SFA_UDOBJ_ACTION_SAVE = "sfa.udobj.action.save";

    /**
     * 类型为空
     */
    String SFA_CAMPAIGNMEMBERS_CAMPAIGNMEMBERSTYPEISNULL = "sfa.campaignmembers.campaignmemberstypeisnull";

    /**
     * 参与状态为空
     */
    String SFA_CAMPAIGNMEMBERS_CAMPAIGNMEMBERSSTATUSISNULL = "sfa.campaignmembers.campaignmembersstatusisnull";

    /**
     * 为空
     */
    String SFA_CAMPAIGNMEMBERS_FILTER_OPERATOR_ISNULL = "sfa.filter.operator.isnull";
    /**
     * 最大库存必须大于安全库存
     */
    String SFA_STOCK_VALIDATE_MSG = "sfa.product.max.stock.must.gth.safety.stock";

    /**
     * 关联同一个价目表的产品属性价目表只能有一个
     */
    String SFA_ATTRIBUTEPRICEBOOK_PRICEBOOK_REPEAT = "sfa.attributepricebook.pricebook.repeat";

    /**
     * 同一种属性定价方式，关联同一个价目表的产品属性价目表只能有一个
     */
    String SFA_ATTRIBUTEPRICEBOOK_PRICINGMODE_PRICEBOOK_REPEAT = "sfa.attributepricebook.pricingmode.pricebook.repeat";

    /**
     * 属性定价方式不能为空
     */
    String SFA_ATTRIBUTEPRICEBOOK_PRICINGMODE_CANNOT_EMPTY = "sfa.attributepricebook.pricingmode.cannot.empty";

    /**
     * 增量属性定价，产品[{0}]定价方式必须为价目表折扣
     */
    String SFA_ATTRIBUTEPRICEBOOK_PRICING_METHOD_PRICE_BOOK_DISCOUNT = "sfa.attributepricebook.pricing.method.price.book.discount";

    /**
     * 增量属性定价，定价方式必须为价目表折扣
     */
    String SFA_ATTRIBUTEPRICEBOOK_PRICING_METHOD_DISCOUNT_WARN = "sfa.attributepricebook.pricing.method.discount.warn";

    /**
     * 同一种属性定价方式，存在相同的产品和价目表数据。
     */
    String SFA_IMPORT_PRICING_METHOD_DUPLICATE_PRODUCT_PRICEBOOK = "sfa.import.pricing.method.duplicate.product.pricebook";

    /**
     * 至少需要选择一个定价属性的属性值
     */
    String SFA_IMPORT_NEED_ONE_PRICING_ATTRIBUTE = "sfa.import.need.one.pricing.attribute.value";

    /**
     * 同一个产品属性值重复
     */
    String SFA_ATTRIBUTEPRICEBOOK_ATTRIBUTE_REPEAT = "sfa.attributepricebook.attribute.repeat";

    /**
     * 折扣为空
     */
    String SFA_ATTRIBUTEPRICEBOOK_DISCOUNT_ISNULL = "sfa.attributepricebook.discount.isnull";
    /**
     * 产品属性以及属性值为空
     */
    String SFA_ATTRIBUTEPRICEBOOK_ATTRIBUTE_GROUP_ISNULL = "sfa.attributepricebook.attributegroup.isnull";
    /**
     * 属性不存在
     */
    String SFA_ATTRIBUTEPRICEBOOK_ATTRIBUTE_NOT_FIND = "sfa.attributepricebook.attribute.not.find";

    /**
     * 客户：{0} {1} 操作人： {2}
     */
    String SFA_ACCOUNT_2_CONTENT = "sfa.account.2.content";

    /**
     * 您的客户：{0}，被合并到：{1}, 您已成为合并后的销售团队成员，负责人：{2}，操作人：{3}
     */
    String SFA_ACCOUNT_MERGE_BE_TEAM_MEMBER_NEW_RECORD = "sfa.account.merge.be.new.team.member.new.record";

    /**
     * 您的客户：{0}，被合并到：{1}, 您不是合并后的销售团队成员，负责人：{2}，操作人：{3}
     */
    String SFA_ACCOUNT_MERGE_NOT_BEE_TEAM_MEMBER_NEW_RECORD = "sfa.account.merge.not.be.new.team.member.new.record";

    /**
     * 您合并了客户：{0}，到客户：{1}
     */
    String SFA_MERGE_ACCOUNT_TO_NEW_RECORD = "sfa.merge.account.to.new.record";

    /**
     * 您的客户：{0}，合并了：{1}, 您已成为合并后的销售团队成员，负责人：{2}，操作人：{3}
     */
    String SFA_ACCOUNT_MERGE_TO_BE_TEAM_MEMBER_NEW_RECORD = "sfa.account.merge.to.be.team.member.new.record";

    /**
     * 合并客户
     */
    String SFA_MERGE_ACCOUNT = "sfa.merge.account";

    /**
     * 您合并了联系人：{0}，到联系人：{1}
     */
    String SFA_MERGE_CONTACT_A_TO_B_NEW_RECORD = "sfa.merge.contact.a.to.b.new.record";

    /**
     * 您的联系人：{0}，被合并到联系人：{1}，操作人：{2}
     */
    String SFA_MERGE_CONTACT_TO_SOURCE_MSG = "sfa.merge.contact.to.source.msg";

    /**
     * 合並聯系人成功
     */
    String SFA_MERGE_CONTACT_SUCCESS_NEW_RECORD = "sfa.merge.contact.success.new.record";

    /**
     * 合并联系人
     */
    String SFA_MERGE_CONTACT_title = "sfa.merge.contact.title";

    /**
     * 合并销售线索
     */
    String SFA_MERGE_LEADS_NEW_RECORD = "sfa.merge.leads.new.record";

    /**
     * 您合并了销售线索：{0}，到销售线索：{1}
     */
    String SFA_MERGE_LEADS_A_B_NEW_RECORD = "sfa.merge.leads.a.b.new.record";

    /**
     * 您的销售线索：{0}，被合并到销售线索：{1}，操作人：{2}
     */
    String SFA_MERGE_LEADS_TO_SOURCE_MSG = "sfa.merge.leads.to.source.msg";

    /**
     * 合并企业库
     */
    String SFA_MERGE_ENTERPRISE_INFO_TITLE = "sfa.merge.enterprise.info.title";

    /**
     * 您的企业库：{0}，被合并到企业库：{1}，操作人：{2}
     */
    String SFA_MERGE_ENTERPRISE_INFO_TO_SOURCE_MSG = "sfa.merge.enterprise.info.to.source.msg";

    /**
     * 合并商机
     */
    String SFA_MERGE_NEW_OPPORTUNITY_TITLE = "sfa.merge.new.opportunity.title";

    /**
     * 您的商机：{0}，被合并到商机：{1}，操作人：{2}
     */
    String SFA_MERGE_NEW_OPPORTUNITY_TO_SOURCE_MSG = "sfa.merge.new.opportunity.to.source.msg";

    /**
     * %s，%s：%s，%s：%s
     * {0} {1} {2} {3} {4}
     */
    String SFA_ALLOCATE_4_CONTENT_NEW_RECORD = "sfa.5.content";


    /**
     * 被授权为{0}{1}的管理员
     */
    String SFA_TO_BE_POOL_ADMIN_NEW_RECORD = "sfa.to.be.pool.admin";
    /**
     * 被授权为{0}{1}的成员
     */
    String SFA_TO_BE_POOL_MEMBER_NEW_RECORD = "sfa.to.be.pool.member";

    /**
     * 被取消授权{0}{1}的管理权限
     */
    String SFA_TO_CANCEL_POOL_ADMIN_NEW_RECORD = "sfa.to.cancel.pool.admin";

    /**
     * 被取消授权{0}{1}的成员权限
     */
    String SFA_TO_CANCEL_POOL_MEMBER_NEW_RECORD = "sfa.to.cancel.pool.member";


    /**
     * 您好，商品已经成功开启！
     */
    String SFA_OPEN_SPU_SUCCESS_CONTENT_NEW_RECORD = "sfa.open.spu.success.content.new.record";
    /**
     * 商品开启成功
     */
    String SFA_OPEN_SPU_SUCCESS_TITLE_NEW_RECORD = "sfa.open.spu.success.title.new.record";

    /**
     * 您好，商品已经成功关闭！
     */
    String SFA_CLOSE_SPU_SUCCESS_CONTENT_NEW_RECORD = "sfa.close.spu.success.content.new.record";

    /**
     * 商品关闭成功
     */
    String SFA_CLOSE_SPU_SUCCESS_TITLE_NEW_RECORD = "sfa.close.spu.success.title.new.record";


    /**
     * 您好，商品开启失败，请重新操作开启！
     */
    String SFA_OPEN_SPU_FAILURE_CONTENT_NEW_RECORD = "sfa.open.spu.failure.content.new.record";

    /**
     * 商品开启失败
     */
    String SFA_OPEN_SPU_FAILURE_TITLE_NEW_RECORD = "sfa.open.spu.failure.title.new.record";

    /**
     * 您好，商品关闭失败，请重新操作关闭！
     */
    String SFA_CLOSE_SPU_FAILURE_CONTENT_NEW_RECORD = "sfa.close.spu.failure.content.new.record";
    /**
     * 商品关闭失败
     */
    String SFA_CLOSE_SPU_FAILURE_TITLE_NEW_RECORD = "sfa.close.spu.failure.title.new.record";

    /**
     * 合作伙伴开启成功
     */
    String SFA_OPEN_PARTNER_SUCCESS_TITLE_NEW_RECORD = "sfa.open.partner.success.title.new.record";
    /**
     * 您好，合作伙伴对象已经成功开启，并且相关配置逻辑也已完成，请放心使用！
     */
    String SFA_OPEN_PARTNER_SUCCESS_CONTENT_NEW_RECORD = "sfa.open.partner.content.title.new.record";

    /**
     * 合作伙伴开启失败
     */
    String SFA_OPEN_PARTNER_FAILURE_TITLE_NEW_RECORD = "sfa.open.partner.failure.title.new.record";
    /**
     * 您好，合作伙伴对象开启失败，请重新操作开启！
     */
    String SFA_OPEN_PARTNER_FAILURE_CONTENT_NEW_RECORD = "sfa.open.partner.failure.content.new.record";
    /**
     * 您好，合作伙伴对象开启失败，请重新操作开启！{0}
     */
    String SFA_OPEN_PARTNER_FAILURE_CONTENT_NEW_RECORD_V2 = "sfa.open.partner.failure.content.new.record.v2";


    /**
     * 产品{0}上存在未关联的属性
     */
    String SFA_PRODUCT_ATTRIBUTE_NO_RELATIONSHIP = "sfa.product.attribute.no.relationship";

    /**
     * 产品{0}上存在未关联的非标属性
     */
    String SFA_PRODUCT_NONSTANDARD_ATTRIBUTE_NO_RELATIONSHIP = "sfa.product.nonstandard.attribute.no.relationship";


    /**
     * 合作伙伴名称已注册！
     */
    String SFA_PARTNER_IS_REGISTERED = "sfa.wechat.partner.is.registered";

    /**
     * 注册失败
     */
    String SFA_PARTNER_REGISTERED_FAILED = "sfa.wechat.registered.failed";

    /**
     * 请检查数据合法性！
     */
    String SFA_PARTNER_CHECK_DATA = "sfa.wechat.check.data";

    /**
     * 数据已存在！
     */
    String SFA_CAMPAIGNMEMBERS_DATAISEXISTS = "sfa.campaignmembers.dataisexists";

    /**
     * {0}, {1}  原币币种不一致
     */
    String MASTER_DETAIL_ORIGINAL_CURRENCY_NOT_SAME = "sfa.master.detail.original.currency.not.same";


    /**
     * 原币币种不允许需改
     */
    String ORIGINAL_CURRENCY_NOT_ALLOW_EDIT = "sfa.original.currency.not.allow.edit";

    /**
     * 作废聚合 存在关联关系
     * 聚合值{0}被规则{1}引用作废失败
     */
    String SFA_AGGREGATEVALUE_INVALID_RELATION = "sfa.aggregatevalue.invalid.relation";

    /**
     * 聚合值计算中不能编辑
     * 聚合值{0}计算中，保存失败
     */
    String SFA_AGGREGATEVALUE_CALCULATESTATUS_RUN = "sfa.aggregatevalue.calculatestatus.run";

    /**
     * 作废聚合 计算中
     * 聚合值{0}计算中不能作废
     */
    String SFA_AGGREGATEVALUE_INVALID_CALCULATESTATUS = "sfa.aggregatevalue.invalid.calculatestatus";

    /**
     * 该聚合规则被生效的价格政策｛0｝引用不可编辑
     */
    String SFA_AGGREGATE_EDIT_REFERENCE = "sfa.aggregate.edit.reference";

    String SFA_AGGREGATE_CONDITION_FIELD_UN_EXISTENT = "sfa.aggregate.condition.field.un.existent";
    /**
     * 日期范围开始时间大于结束时间
     */
    String SFA_AGGREGATEVALUE_DATERANGE_START_BIG = "sfa.aggregatevalue.daterange.start.big";

    /**
     * 日期范围为空
     */
    String SFA_AGGREGATEVALUE_DATERANGE_EMPTY = "sfa.aggregatevalue.daterange.empty";


    /**
     * 聚合维度为空
     */
    String SFA_AGGREGATEVALUE_DIMENSION_EMPTY = "sfa.aggregatevalue.dimension.empty";

    /**
     * 日期字段为空
     */
    String SFA_AGGREGATEVALUE_DATE_FIELD_EMPTY = "sfa.aggregatevalue.datefield.empty";

    /**
     * 聚合日期范围不能大于13个月
     * 日期范围不能大于{0}个月
     */
    String SFA_AGGREGATEVALUE_DATERANGE_LAST = "sfa.aggregatevalue.daterange.last";


    /**
     * 添加产品快速维护价目表
     * 价目表重复
     */
    String SFA_PRICEBOOKPRODUCT_PRICEBOOK_REPEAT = "sfa.pricebookproduct.pricebook.repeat";

    /**
     * 添加产品快速维护价目表
     * 只能保存同一产品
     */
    String SFA_PRICEBOOKPRODUCT_PRODUCT_EQUALLY = "sfa.pricebookproduct.pricebook.equally";

    /**
     * 添加产品快速维护价目表
     * 过滤后台配置条件筛选
     */
    String SFA_PRICEBOOKPRODUCT_PRODUCT_WHERESFILTERE = "sfa.pricebookproduct.pricebook.wheresfiltere";

    /**
     * {0}已作废或删除，不允许恢复{1}!
     */
    String SFA_ACCOUNT_CANNOT_ALLOW_RECOVER = "sfa.account.cannot.allow.recover";
    /**
     * 不在同一个组织的{0}不能合并
     */
    String SFA_ACCOUNT_NOTINSAMEORGCANNOTMERGE = "sfa.account.notinsameorgcannotmerge";

    /**
     * 所选数据已是该数据的上级或子级
     */
    String SFA_MARKETING_EVENT_CHILDISFATHER = "sfa.marketing.event.childisfather";

    /**
     * 只能选择已转换的{0}
     */
    String SFA_ACCOUNT_CHOOSETRANSFORMEDLEADS = "sfa.account.choosetransformedleads";

    /**
     * 当前选择的{0}，已被其他{1}绑定，请选用其他{2}！
     */
    String SFA_ACCOUNT_CANNOTUSETHELEADS = "sfa.account.cannotusetheleads";

    /**
     * 该{0}已有主线索，不能更换
     */
    String SFA_ACCOUNT_CANNOTCHANGELEADS = "sfa.account.cannotchangeleads";

    /**
     * 已建立层级关系不支持作废
     */
    String SFA_OBJ_HAS_PATH_NOT_ALLOW_INVALID = "sfa.obj.has.path.not.allow.invalid";

    /**
     * 上级数据不存在，请先导入上级后再导入下级。
     */
    String SFA_OBJ_PARENT_ID_NOT_EXIST = "sfa.obj.parent.id.not.exist";

    /**
     * 层级不能超过{}级
     */
    String SFA_OBJ_PATH_PARENT_ID_NOT_EXCEED = "sfa.obj.path.parent.id.not.exceed";

    /**
     * 指定赠品数量
     */
    String SFA_SALESORDERPRODUCT_GIFT_QUOTA = "sfa.salesorderproduct.gift.quota";

    String SFA_FIXED_GIFT_QUANTITY = "sfa.fixed.gift.quantity";
    /**
     * 指定赠品金额
     */
    String SFA_FIXED_GIFT_MONEY = "sfa.fixed.gift.money";
    /**
     * 所有赠品数量
     */
    String SFA_ALL_GIFT_QUANTITY = "sfa.all.gift.quantity";
    /**
     * 所有赠品金额
     */
    String SFA_ALL_GIFT_MONEY = "sfa.all.gift.money";

    /**
     * 指定赠品数量+当前客户
     */
    String SFA_SALESORDERPRODUCT_GIFT_ACCOUNT_QUOTA = "sfa.salesorderproduct.gift.account.quota";
    String SFA_MASTER_FIXED_GIFT_QUANTITY = "sfa.master.fixed.gift.quantity";
    /**
     * 指定赠品金额+当前客户
     */
    String SFA_ACCOUNT_FIXED_GIFT_MONEY = "sfa.account.fixed.gift.money";
    String SFA_MASTER_FIXED_GIFT_MONEY = "sfa.master.fixed.gift.money";

    /**
     * 所有赠品金额+当前客户
     */
    String SFA_ACCOUNT_ALL_GIFT_MONEY = "sfa.account.all.gift.money";
    String SFA_MASTER_ALL_GIFT_MONEY = "sfa.master.all.gift.money";
    /**
     * 所有赠品数量+当前客户
     */
    String SFA_ACCOUNT_ALL_GIFT_QUANTITY = "sfa.account.all.gift.quantity";
    String SFA_MASTER_ALL_GIFT_QUANTITY = "sfa.master.all.gift.quantity";


    /**
     * 片区指定赠品数量
     */
    String SFA_AREA_FIXED_GIFT_QUANTITY = "sfa.area.fixed.gift.quantity";
    /**
     * 片区指定赠品金额
     */
    String SFA_AREA_FIXED_GIFT_MONEY = "sfa.area.fixed.gift.money";
    /**
     * 片区所有赠品数量
     */
    String SFA_AREA_ALL_GIFT_QUANTITY = "sfa.area.all.gift.quantity";
    /**
     * 片区所有赠品金额
     */
    String SFA_AREA_ALL_GIFT_MONEY = "sfa.area.all.gift.money";
    /**
     * 经销商指定赠品数量
     */
    String SFA_DEALER_FIXED_GIFT_QUANTITY = "sfa.dealer.fixed.gift.quantity";
    String SFA_DETAIL_FIXED_GIFT_QUANTITY = "sfa.detail.fixed.gift.quantity";
    /**
     * 经销商指定赠品金额
     */
    String SFA_DEALER_FIXED_GIFT_MONEY = "sfa.dealer.fixed.gift.money";
    String SFA_DETAIL_FIXED_GIFT_MONEY = "sfa.detail.fixed.gift.money";
    /**
     * 经销商所有赠品数量
     */
    String SFA_DEALER_ALL_GIFT_QUANTITY = "sfa.dealer.all.gift.quantity";
    String SFA_DETAIL_ALL_GIFT_QUANTITY = "sfa.detail.all.gift.quantity";
    /**
     * 经销商所有赠品金额
     */
    String SFA_DEALER_ALL_GIFT_MONEY = "sfa.dealer.all.gift.money";
    String SFA_DETAIL_ALL_GIFT_MONEY = "sfa.detail.all.gift.money";
    /**
     * 主品优惠数量
     */
    String SFA_MAIN_PRODUCT_QUANTITY = "sfa.main.product.quantity";
    /**
     * 主品优惠金额
     */
    String SFA_MAIN_PRODUCT_MONEY = "sfa.main.product.money";
    /**
     * 用户主品优惠数量
     */
    String SFA_ACCOUNT_MAIN_PRODUCT_QUANTITY = "sfa.account.main.product.quantity";
    String SFA_MASTER_MAIN_PRODUCT_QUANTITY = "sfa.master.main.product.quantity";
    /**
     * 用户主品优惠金额
     */
    String SFA_ACCOUNT_MAIN_PRODUCT_MONEY = "sfa.account.main.product.money";
    String SFA_MASTER_MAIN_PRODUCT_MONEY = "sfa.master.main.product.money";
    /**
     * 片区主品优惠数量
     */
    String SFA_AREA_MAIN_PRODUCT_QUANTITY = "sfa.area.main.product.quantity";
    /**
     * 片区主品优惠金额
     */
    String SFA_AREA_MAIN_PRODUCT_MONEY = "sfa.area.main.product.money";
    /**
     * 经销商主品优惠金额
     */
    String SFA_DEALER_MAIN_PRODUCT_MONEY = "sfa.dealer.main.product.money";
    String SFA_DETAIL_MAIN_PRODUCT_MONEY = "sfa.detail.main.product.money";
    /**
     * 经销商主品优惠数量
     */
    String SFA_DEALER_MAIN_PRODUCT_QUANTITY = "sfa.dealer.main.product.quantity";
    String SFA_DETAIL_MAIN_PRODUCT_QUANTITY = "sfa.detail.main.product.quantity";


    String SFA_ALL_GIFT_MONEY_POLICY = "sfa.all.gift.money.policy";
    String SFA_ACCOUNT_ALL_GIFT_MONEY_POLICY = "sfa.account.all.gift.money.policy";
    String SFA_MASTER_ALL_GIFT_MONEY_POLICY = "sfa.master.all.gift.money.policy";


    String SFA_AREA_ALL_GIFT_MONEY_POLICY = "sfa.area.all.gift.money.policy";
    String SFA_DEALER_ALL_GIFT_MONEY_POLICY = "sfa.dealer.all.gift.money.policy";
    String SFA_DETAIL_ALL_GIFT_MONEY_POLICY = "sfa.detail.all.gift.money.policy";
    // 自定义限量对象类型[]未配置完整，请联系您的供应商或纷享客服。
    String SFA_CUSTOM_LIMITOBJTYPE_UN_CONFIG = "sfa.custom.limit_obj_type.un_config";

    // 当前有其他单据正在操作，价格政策的限额限量控制被锁定，请稍后再试
    String SFA_POLICY_OCCUPY_LOCK = "sfa.policy.occupy.lock";
    /**
     * 不能添加负责人角色到相关团队
     */
    String SFA_ACCOUNT_CANNOT_INSERT_OWNER_TEAM_MEMBER_ROLE = "sfa.account.cannot_insert_owner_team_member_role";
    String SFA_STOCK_DATA_TYPE_ERROR = "sfa.stock.data.type.error";

    /**
     * 抱歉，{0}的保有量已达上限，刚刚更换客户负责人时更换商机负责人的操作未成功。失败商机如下：{1}
     */
    String SFA_NEWOPPORTUNITY_OWNER_SHIP_OVERRUN = "sfa.newopportunity.owner_ship_overrun";

    /**
     * 商机负责人更换失败
     */
    String SFA_NEWOPPORTUNITY_OWNER_CHANGE_FAILURE = "sfa.newopportunity.owner_change_failure";

    /**
     * 只能更新{0}，不能更新其他对象
     */
    String SFA_ACCOUNT_UPDATEERROR = "sfa.account.updateerror";


    /**
     * 价格政策明细限量赠品为空
     */
    String SFA_PRICE_POLICY_LIMIT_PRODUCT_EMPTY = "sfa.price.policy.limit.product.empty";
    /**
     * 用户为空
     */
    String SFA_PRICE_POLICY_LIMIT_ACCOUNT_EMPTY = "sfa.price.policy.limit.account.empty";

    /**
     * 片区为空
     */
    String SFA_PRICE_POLICY_LIMIT_AREA_EMPTY = "sfa.price.policy.limit.area.empty";

    /**
     * 经销商为空
     */
    String SFA_PRICE_POLICY_LIMIT_DEALER_EMPTY = "sfa.price.policy.limit.dealer.empty";

    /**
     * 价格政策明细限量价格规则不存在
     */
    String SFA_PRICE_POLICY_LIMIT_RULE_ERROR = "sfa.price.policy.limit.rule.error";

    /**
     * 重复
     */
    String SFA_PRICE_POLICY_LIMIT_EXIST = "sfa.price.policy.limit.exist";

    String SFA_PRICE_POLICY_MUST_GIFT_AMOUNT = "sfa.price.policy.must.gift.amount";

    String SFA_PRICE_POLICY_DIMENSION_LIMIT_TYPE = "sfa.price.policy.dimension_limit_type";

    String SFA_PRICE_POLICY_DIMENSION = "sfa.price.policy.dimension";

    String SFA_PRICE_POLICY_RANGE = "sfa.price.policy.range";
    /**
     * 未生效或者审核中的{0}不允许修改{1}
     */
    String SFA_ACCOUNT_CANNOTCHANGEHIGHSEAS = "sfa.account.cannotchangehighseas";

    /**
     * 主从同时新建时，不能同时创建主地址或者默认地址
     */
    String SFA_ACCOUNT_CANNOTCREATEMAINADDRESS = "sfa.account.cannotcreatemainaddress";

    /**
     * 主从同时新建时，不能同时创建默认财务信息
     */
    String SFA_ACCOUNT_CANNOTCREATEDEFAULTFININFO = "sfa.account.cannotcreatedefaultfininfo";


    /**
     * 通过部门批量新建{0}
     */
    String SFA_POOL_BULKADDPOOL = "sfa.pool.bulkaddpool";
    /*
    通过部门批量生成{0}的配置已经处理完毕，请进入后台查看效果
     */
    String SFA_POOL_BULKADDPOOLDATADONE = "sfa.pool.bulkaddpooldatadone";

    /*
    通过部门批量生成{0}的配置已经处理完毕，本次成功同步{1}个，失败{2}个 {3}。
     */ String SFA_POOL_BULKADDPOOLDATADONEANDFAILURE = "sfa.pool.bulkaddpooldatadoneandfailure";


    /**
     * 通过互联企业自动生成{0}的结果说明
     */
    String SFA_POOL_BULKADDPOOL_ENTERPRISE_DESC = "sfa.pool.bulkaddpool.enterprise_desc";

    /**
     * 通过互联企业批量生成{0}的配置已经处理完毕，本次成功同步{1}个，失败{2}个。
     */
    String SFA_POOL_BULKADDPOOLDATADONEANDFAILURE_ENTERPRISE = "sfa.pool.bulkaddpooldatadoneandfailure.enterprise";


    /**
     * 批量编辑{0}
     */
    String SFA_POOL_BULKUPDATEPOOL = "sfa.pool.bulkupdatepool";
    /**
     * 批量编辑{0}的配置已经处理完毕，本次成功同步{1}个，失败{2}个 {3}。
     */
    String SFA_POOL_BULKUPDATEPOOLDATADONEANDFAILURE = "sfa.pool.bulkupdatepooldatadoneandfailure";
    /**
     * 批量编辑{0}的配置已经处理完毕，本次成功同步{1}个，失败{2}个，失败公海：{3}，失败原因：{4}
     */
    String SFA_POOL_BULKUPDATEPOOLDATADONEANDFAILUREHIGHSEA = "sfa.pool.bulkupdatepooldatadoneandfailureHighSea";

    /**
     * 批量编辑{0}的配置已经处理完毕，本次成功同步{1}个，失败{2}个，失败线索池：{3}，失败原因：{4}
     */
    String SFA_POOL_BULKUPDATEPOOLDATADONEANDFAILURELEADSPOOL = "sfa.pool.bulkupdatepooldatadoneandfailureLeadsPool";

    /**
     * 处理方式设置有误，请重新设置
     */
    String SFA_DUPLICATED_SETCONFIGERROR = "sfa.duplicated.setconfigerror";
    /**
     * 自动更新已有线索
     */
    String SFA_DUPLICATED_AUTOCHANGELEADS = "sfa.duplicated.autochangeleads";

    /**
     * 控制方式只支持客户
     */
    String SFA_PRICE_POLICY_LIMIT_ACCOUNT_MODE = "sfa.price.policy.limit.account.mode";

    /**
     * 未开启价目表
     */
    String SFA_COMPANY_UNALBE_PRICEBOOK = "sfa.company.unable.pricebook";
    /**
     * 【复制{0}价目表明细】执行完毕 执行人:{1}
     */
    String SFA_ASYN_COPY_NOTICE_TITLE = "sfa.asyn.copy.notice.title";

    /**
     * 本次操作覆盖{0}条 【{1}价目表明细】 复制到 【{2}价目表明细】，执行成功{3}条，失败{4}条；
     */
    String SFA_ASYN_COPY_NOTICE_CONTENT = "sfa.asyn.copy.notice.content";

    /**
     * 添加成功{0}条，失败{1}条（失败原因：企业微信userid已存在）
     */
    String SFA_EXTERNAL_BULK_IMPORT_RESULT = "sfa.external.bulk.import.result";
    /**
     * 门槛金额，不能大于满额
     */
    String SFA_COUPON_PLAN_LOWER_LIMIT_AMOUNT = "sfa.coupon.plan.lower.limit.amount";
    /**
     * 门槛金额，不能为负值
     */
    String SFA_COUPON_PLAN_LOWER_LIMIT_AMOUNT_CANNOT_LT_ZERO = "sfa.coupon.plan.lower.limit.amount.cannot.lt.zero";
    /**
     * 面额，不能为负值
     */
    String SFA_COUPON_PLAN_AMOUNT_CANNOT_LT_ZERO = "sfa.coupon.plan.amount.cannot.lt.zero";
    /**
     * 有异常，但是未获取到异常信息
     */
    String SFA_GET_WARN_INFO = "sfa.get.warn.info";
    /**
     * 未找到产品条件的翻译类，请确定条件类型
     */
    String SFA_COUPON_NOT_FIND_CONDITION_TYPE = "sfa.coupon.not.find.condition.type";

    /**
     * 请选择不超过10条记录
     */
    String SFA_LIMIT_TEN_CHECK = "sfa.qywx.import.limit.check";

    /**
     * 导入完毕。其中成功%s条、失败%s条、更新%s条。
     */
    String SFA_NEW_EXTERNAL_BULK_IMPORT_RESULT = "sfa.new.external.bulk.import.result";

    /**
     * 【客户数据自动同步】执行完毕
     */
    String SFA_ACCOUNT_AUTO_SYNC_DONE_TITLE = "sfa.account.auto.sync.done.title";

    /**
     * 【企业微信客户数据同步】
     */
    String SFA_ENTERPRISE_WECHAT_DATA_SYNC_TITLE = "sfa.enterprise.wechat.data.sync.title";
    /**
     * 企业微信UserId字段不能为空，请检查映射规则
     */
    String SFA_ENTERPRISE_WECHAT_USER_ID_IS_NULL = "sfa.enterprise.wechat.user.id.is.null";

    /**
     * 您好，最近24小时新增的企业微信客户信息已同步完毕，其中 导入成功{0}条、失败{1}条、更新{2}条。
     */
    String SFA_ACCOUNT_AUTO_SYNC_DONE_CONTENT = "sfa.account.auto.sync.done.content";

    /**
     * 企微客户external_userid:{0}，name{1}在CRM中已经存在，负责人是{2}，导入失败。
     */
    String SFA_IMPORT_FAILED_FOR_WECHAT_DATA_CONTENT = "sfa.import.failed.for.wechat.data.content";
    /**
     * 系统（通过【客户数据自动同步】功能） 编辑  数据主属性  企业微信userID ：空->
     */
    String SFA_EXTERNAL_IMPORT_MODIFY_LOG = "sfa.external.import.modify.log";
    /**
     * 成功导入数据， 来源：企业微信 ，企微好友昵称：{0} ，external_userid：{1}
     */
    String SFA_EXTERNAL_IMPORT_SUCCESS = "sfa.external.import.success";

    /**
     * 您导入的企微客户在纷享CRM数据中已存在，name：{0}，建议您前往CRM线索/公海池中查看详情领取后再进行导入。
     */
    String SFA_IMPORT_HAVE_NO_OWNER_DATA_CONTENT = "sfa.import.have.no.owner.data.content";

    /**
     * 该企微客户在CRM线索中已经存在，负责人是%s，导入失败。
     */
    String SFA_CURRENT_DATA_EXISTS = "sfa.current.qywx.data.exists";

    /**
     * 申请加入相关团队
     */
    String SFA_APPLY_JOIN_RELATED_TEAM_TITLE = "sfa.qywx.apply.join.related.team.title";
    /**
     * {0}向你申请加入 {1} 的相关团队，请处理。
     */
    String SFA_APPLY_JOIN_RELATED_TEAM_CONTENT = "sfa.qywx.apply.join.related.team.content";

    /**
     * 该企业已开启多组织，不允许导入客户对象
     */
    String SFA_CAN_NOT_IMPORT_BY_OPEN_MULT_ORG = "sfa.can.not.import.by.open.mult.org";
    /**
     * 该企微客户无手机号不允许同步，请先维护好手机号再同步。
     */
    String SFA_IMPORT_REFUSE_BY_NO_PHONE = "sfa.import.refuse.by.no.phone";

    /**
     * 企微客户external_userid:{0}，name:{2}无手机号不允许同步，请先维护好手机号再同步。
     */
    String SFA_IMPORT_REFUSE_BY_NO_PHONE_CONTENT = "sfa.import.refuse.by.no.phone.content";
    /**
     * 返利使用规则不能为空
     */
    String SFA_REBATE_RULE_CANNOT_EMPTY = "sfa.rebate.rule.cannot.empty";
    /**
     * 规则使用的对象不存在
     */
    String PRICE_POLICY_OBJECT_NOT_EXIST = "price_policy.object_not_exist";
    /**
     * 规则使用的字段不存在
     */
    String PRICE_POLICY_FIELD_NOT_EXIST = "price_policy.field_not_exist";

    /**
     * 规则条件使用的聚合值不存在
     */
    String PRICE_POLICY_AGGREGATE_NOT_EXIST = "price_policy.aggregate_not_exist";

    /**
     * 规则中，计算类型不可为空
     */
    String REBATE_RULE_CONTENT_CALCULATE_CANNOT_EMPTY = "rebate.rule.content.calculate.cannot.empty";
    /**
     * 规则中，存在未知的计算类型
     */
    String REBATE_RULE_CONTENT_CALCULATE_UN_KNOW = "rebate.rule.content.calculate.un.know";
    /**
     * 规则计算类型是按每满计算，每满计算信息不可为空
     */
    String REBATE_RULE_CONTENT_CYCLE_CANNOT_EMPTY = "rebate.rule.content.cycle.cannot.empty";

    /**
     * 优惠券规则条件不能为空
     */
    String SFA_COUPON_RULE_CONDITION_CANNOT_EMPTY = "sfa.coupon.rule.condition.cannot.empty";

    /**
     * 复杂产品条件，产品数量不能超过200
     */
    String SFA_COUPON_RULE_CONDITION_HARD_PRODUCT_MAX = "sfa.coupon.rule.condition.hard.product.not.open.multiple.max";

    /**
     * 复杂产品条件，产品数量不能超过20
     */
    String SFA_COUPON_RULE_CONDITION_HARD_PRODUCT_OPEN_MULTIPLE_MAX = "sfa.coupon.rule.condition.hard.product.open.multiple.max";

    /**
     * 必含商品关系不能为空，请选择
     */
    String SFA_COUPON_RULE_CONDITION_MUST_REAL_CANNOT_EMPTY = "sfa.coupon.rule.condition.must.real.cannot.empty";

    /**
     * 必含关系为必含任N，N值不能为空，请录入N值
     */
    String SFA_COUPON_RULE_CONDITION_N_CANNOT_EMPTY = "sfa.coupon.rule.condition.n.cannot.empty";

    /**
     * 存在必含产品，必含数量不能为空
     */
    String SFA_COUPON_RULE_CONDITION_CONTAIN_NUM_CANNOT_EMPTY = "sfa.coupon.rule.condition.contain.num.cannot.empty";

    /**
     * 产品范围中，%s 已经失效
     */
    String SFA_COUPON_RULE_CONDITION_PRODUCT_UN_EFFECTIVE = "sfa.coupon.rule.condition.product.un.effective";

    /**
     * 存在必含商品，必含商品数量必须大于0
     */
    String SFA_COUPON_RULE_CONDITION_MUST_CONTAINS_NUM = "sfa.coupon.rule.condition.must.contains.num";
    /**
     * 生效日期，失效日期不可为空，请录入
     */
    String SFA_COUPON_PLAN_START_END_DATE_CANNOT_EMPTY = "sfa.coupon.plan.start.end.date.cannot.empty";
    /**
     * 失效日期，必须晚于生效日期，请修改
     */
    String SFA_COUPON_PLAN_START_END_DATE_LESS = "sfa.coupon.plan.start.end.date.less";
    /**
     * 优惠券方案使用类型不可为空，请录入
     */
    String SFA_COUPON_PLAN_USE_TYPE_CANNOT_EMPTY = "sfa.coupon.plan.use.type.cannot.empty";
    /**
     * 规则类型不可为空，请录入
     */
    String SFA_COUPON_PLAN_CONDITION_TYPE_CANNOT_EMPTY = "sfa.coupon.plan.condition.type.cannot.empty";

    // 价格政策限额限量提醒
    String SFA_RULE_GIFT_FIXED_GIFT_QUANTITY = "sfa.rule.gift.fixed.gift.quantity";
    String SFA_RULE_GIFT_FIXED_GIFT_AMOUNT = "sfa.rule.gift.fixed.gift.amount";
    String SFA_RULE_GIFT_FIXED_MASTER_GIFT_QUANTITY = "sfa.rule.gift.fixed.master.gift.quantity";
    String SFA_RULE_GIFT_FIXED_MASTER_GIFT_AMOUNT = "sfa.rule.gift.fixed.master.gift.amount";
    String SFA_RULE_GIFT_FIXED_DETAIL_GIFT_QUANTITY = "sfa.rule.gift.fixed.detail.gift.quantity";
    String SFA_RULE_GIFT_FIXED_DETAIL_GIFT_AMOUNT = "sfa.rule.gift.fixed.detail.gift.amount";
    String SFA_RULE_GIFT_ALL_GIFT_QUANTITY = "sfa.rule.gift.all.gift.quantity";
    String SFA_RULE_GIFT_ALL_GIFT_AMOUNT = "sfa.rule.gift.all.gift.amount";
    String SFA_RULE_GIFT_ALL_MASTER_GIFT_QUANTITY = "sfa.rule.gift.all.master.gift.quantity";
    String SFA_RULE_GIFT_ALL_MASTER_GIFT_AMOUNT = "sfa.rule.gift.all.master.gift.amount";
    String SFA_RULE_GIFT_ALL_DETAIL_GIFT_QUANTITY = "sfa.rule.gift.all.detail.gift.quantity";
    String SFA_RULE_GIFT_ALL_DETAIL_GIFT_AMOUNT = "sfa.rule.gift.all.detail.gift.amount";
    String SFA_RULE_SELF_ORIGINAL_QUANTITY = "sfa.rule.self.original.quantity";
    String SFA_RULE_SELF_ORIGINAL_AMOUNT = "sfa.rule.self.original.amount";
    String SFA_RULE_SELF_MASTER_ORIGINAL_QUANTITY = "sfa.rule.self.master.original.quantity";
    String SFA_RULE_SELF_MASTER_ORIGINAL_AMOUNT = "sfa.rule.self.master.original.amount";
    String SFA_RULE_SELF_DETAIL_ORIGINAL_QUANTITY = "sfa.rule.self.detail.original.quantity";
    String SFA_RULE_SELF_DETAIL_ORIGINAL_AMOUNT = "sfa.rule.self.detail.original.amount";
    String SFA_POLICY_GIFT_FIXED_GIFT_QUANTITY = "sfa.policy.gift.fixed.gift.quantity";
    String SFA_POLICY_GIFT_FIXED_GIFT_AMOUNT = "sfa.policy.gift.fixed.gift.amount";
    String SFA_POLICY_GIFT_FIXED_MASTER_GIFT_QUANTITY = "sfa.policy.gift.fixed.master.gift.quantity";
    String SFA_POLICY_GIFT_FIXED_MASTER_GIFT_AMOUNT = "sfa.policy.gift.fixed.master.gift.amount";
    String SFA_POLICY_GIFT_FIXED_DETAIL_GIFT_QUANTITY = "sfa.policy.gift.fixed.detail.gift.quantity";
    String SFA_POLICY_GIFT_FIXED_DETAIL_GIFT_AMOUNT = "sfa.policy.gift.fixed.detail.gift.amount";
    String SFA_POLICY_GIFT_ALL_GIFT_AMOUNT = "sfa.policy.gift.all.gift.amount";
    String SFA_POLICY_GIFT_ALL_GIFT_QUANTITY = "sfa.policy.gift.all.gift.quantity";
    String SFA_POLICY_GIFT_ALL_MASTER_GIFT_AMOUNT = "sfa.policy.gift.all.master.gift.amount";
    String SFA_POLICY_GIFT_ALL_MASTER_GIFT_QUANTITY = "sfa.policy.gift.all.master.gift.quantity";
    String SFA_POLICY_GIFT_ALL_DETAIL_GIFT_AMOUNT = "sfa.policy.gift.all.detail.gift.amount";
    String SFA_POLICY_GIFT_ALL_DETAIL_GIFT_QUANTITY = "sfa.policy.gift.all.detail.gift.quantity";
    String SFA_POLICY_SELF_ORIGINAL_AMOUNT = "sfa.policy.self.original.amount";
    String SFA_POLICY_SELF_ORIGINAL_QUANTITY = "sfa.policy.self.original.quantity";
    String SFA_POLICY_SELF_MASTER_ORIGINAL_QUANTITY = "sfa.policy.self.master.original.quantity";
    String SFA_POLICY_SELF_MASTER_ORIGINAL_AMOUNT = "sfa.policy.self.master.original.amount";
    String SFA_POLICY_SELF_DETAIL_ORIGINAL_QUANTITY = "sfa.policy.self.detail.original.quantity";
    String SFA_POLICY_SELF_DETAIL_ORIGINAL_AMOUNT = "sfa.policy.self.detail.original.amount";
    /**
     * 产品条件为指定范围，不支持 或 关联,请修改
     */
    String SFA_COUPON_PLAN_CONDITION_UNSUPPORTED_OR = "sfa.coupon.plan.condition.unsupported.or";
    /**
     * 返利单客户不可为空，请录入
     */
    String SFA_REBATE_ACCOUNT_CANNOT_EMPTY = "sfa.rebate.account.cannot.empty";
    /**
     * 客户数据作废，或已经删除，请重新选择
     */
    String SFA_REBATE_ACCOUNT_DATA_NOT_EFFECTIVE = "sfa.rebate.account.data.not.effective";
    /**
     * 使用方式不可为空，请选择
     */
    String SFA_REBATE_USE_TYPE_CANNOT_EMPTY = "sfa.rebate.use.type.cannot.empty";
    /**
     * 面额可为空，请录入
     */
    String SFA_REBATE_SUM_AMOUNT_CANNOT_EMPTY = "sfa.rebate.sum.amount.cannot.empty";
    /**
     * 面额必须大于0,请修改
     */
    String SFA_REBATE_SUM_AMOUNT_MORE_THEN_ZERO = "sfa.rebate.sum.amount.more.then.zero";

    /**
     * 启用状态不可为空，请选择
     */
    String SFA_REBATE_ACTIVE_STATUS_CANNOT_EMPTY = "sfa.rebate.active.status.cannot.empty";

    /**
     * 优惠券方案数据作废，或已经删除，请重新选择
     */
    String SFA_REBATE_INSTANCE_PLAN_DATA_NOT_EFFECTIVE = "sfa.rebate.instance.plan.data.not.effective";

    /**
     * 优惠券方案已经关联优惠券实例，只可以修改以下字段：名称、有效期、备注
     */
    String SFA_COUPON_PLAN_EDIT_FIELD = "sfa.coupon.plan.edit.field";

    /**
     * 返利单已经使用，只可以修改以下字段：总量、生效日期、失效日期
     */
    String SFA_REBATE_CAN_EDIT_FIELD = "sfa.rebate.can.edit.field";

    /**
     * 优惠券方案已经产生优惠券实例，不可作废或删除
     */
    String SFA_COUPON_PLAN_CANNOT_DELETE = "sfa.coupon.plan.cannot.delete";

    /**
     * 优惠券实例已经使用，不可编辑、作废或删除
     */
    String SFA_COUPON_INSTANCE_CANNOT_DELETE = "sfa.coupon.instance.cannot.delete";

    /**
     * 优惠券方案已经产生优惠券实例，生效日期只允许提前，失效日期只允许延后
     */
    String SFA_COUPON_INSTANCE_START_DATE_END_DATA = "sfa.coupon.instance.start.date.end.date";

    /**
     * 返利单已经使用，生效日期只允许提前，失效日期只允许延后
     */
    String SFA_REBATE_INSTANCE_START_DATE_END_DATA = "sfa.rebate.instance.start.date.end.date";
    /**
     * 该返利单已经使用，不可作废、删除
     */
    String SFA_REBATE_CANNOT_DELETE = "sfa.rebate.cannot.delete";

    /**
     * 返利单总量，必须大于使用量，请修改
     */
    String SFA_REBATE_EDIT_SUM_AMOUNT_MUST_GT_USED_AMOUNT = "sfa.rebate.edit.sum.amount.must.gt.used.amount";
    /**
     * 返利产生来源配置不支持删除选项
     */
    String SFA_REBATE_POLICY_CONFIG_NOT_DELETE = "sfa.rebate.policy.config.not.delete";

    /**
     * {0}对象不存在
     */
    String SFA_REBATE_POLICY_SOURCE_CONFIG_NOT_HAVE_DESCRIBE = "sfa.rebate.policy.source.config.not.have.describe";

    /**
     * 增加返利来源配置，必须开启返利单
     */
    String SFA_REBATE_POLICY_SOURCE_CONFIG_MUST_OPEN_REBATE = "sfa.rebate.policy.source.config.must.open.rebate";


    /**
     * 上级数据存在异常， path字段为空
     */
    String PARENT_DATA_PATH_EMPTY = "parent.data.path.empty";
    /**
     * 上级数据不存在
     */
    String PARENT_DATA_NOT_EXISTS = "parent.data.not.exists";

    /**
     * 必含关系为必含任N，N值不能超过必含产品总数
     */
    String SFA_COUPON_RULE_CONDITION_N_CANNOT_GT_MUST_CONTAIN = "sfa.coupon.rule.condition.n.gt.must.contain";

    /**
     * {0}重复，已生成{1}，不再生成{0}！
     */
    String SFA_LEADS_REPEAT = "sfa.leads.repeat";

    // 对象名称不能重复
    String OBJECT_NAME_REPEATED = "sfa.object.name.repeat";
    /**
     * 存在不可使用的返利单，请重新选择。
     */
    String SFA_REBATE_CANNOT_USE = "sfa.rebate.cannot.use";

    /**
     * 负责人已有值
     */
    String SFA_OWNER_VALUE_NOT_NULL = "sfa.owner.value.not.null";

    /**
     * 存在不可使用的优惠券，请重新选择。
     */
    String SFA_COUPON_CANNOT_USE = "sfa.coupon.cannot.use";


    /**
     * 返利单%s，使用超量，请重新选择。
     */
    String SFA_REBATE_USE_OVER = "sfa.rebate.use.over";

    /**
     * 优惠券%s，已经被使用，请重新选择。
     */
    String SFA_COUPON_USE_OVER = "sfa.coupon.use.over";

    /**
     * 当前有其他单据正在操作，当单使用的优惠券被锁定，请稍后再试
     */
    String SFA_COUPON_LOCK = "sfa.coupon.lock";

    /**
     * 当前有其他单据正在操作，当单使用的返利单被锁定，请稍后再试
     */
    String SFA_REBATE_LOCK = "sfa.rebate.lock";

    /**
     * 简易版价格政策必须含有订单产品组 - 生成的聚合规则必须含有订单产品组
     */
    String SFA_SIMPLE_PRICE_POLICY_MUST_CONTAINS_GROUP = "sfa.simple.price.policy.must.contains.group";
    /**
     * 简易价格政策属性不能为null和空字符串  -必填项不能为空,请输入
     */
    String SFA_SIMPLE_PRICE_POLICY_ALL_ATTRIBUTE_MUST_NOT_NULL = "sfa.simple_price_policy.all.attribute.must.not.null";
    /**
     * 简易价格政策多阶梯不能每满  - 多阶梯不支持每满功能
     */
    String SFA_SIMPLE_PRICE_POLICY_CYCLE_WITHOUT_LADDER = "sfa.simple_price_policy.cycle.without.ladder";
    /**
     * 简易价格政策经销商不能为空 -  经销商不能为空,请选择
     */
    String SFA_SIMPLE_PRICE_POLICY_DEALER_MUST_HAVE = "sfa.simple_price_policy.dealer.must.have";
    /**
     * 简易价格政策阶梯不能为空 - 阶梯不能为空,请输入
     */
    String SFA_SIMPLE_PRICE_POLICY_LADDER_MUST_HAVE = "sfa.simple_price_policy.ladder.must.have";
    /**
     * 简易价格政策阶梯没有条件行  - 选择产品条件不能为空,请输入
     */
    String SFA_SIMPLE_PRICE_POLICY_CONDITION_MUST_HAVE = "sfa.simple_price_policy.condition.must.have";
    /**
     * 简易价格政策不选产品,只选分类的时候,分类不能重复  - 只按分类选择产品时,分类不能重复,请更改
     */
    String SFA_SIMPLE_PRICE_POLICY_ONLY_CATEGORY_MUST_NOT_REPETITION = "sfa.simple_price_policy.only.category.must.not.repetition";
    /**
     * 简易价格政策固定赠品,固定赠品不能空 -  固定赠品不能为空,请输入
     */
    String SFA_SIMPLE_PRICE_POLICY_GIFT_FIXED_MUST_GIFTLIST = "sfa.simple_price_policy.gift.fixed.must.giftlist";
    /**
     * 简易价格政策可选赠品,特定赠品和条件赠品不能都为空  - 指定赠品和可选赠品不能同时为空,请修改
     */
    String SFA_SIMPLE_PRICE_POLICY_GIFT_OPTIONAL_MUST_GIFTLIST_OR_GIFTCONDITION = "sfa.simple_price_policy.gift.optional.must.giftlist.or.giftcondition";
    /**
     * 简易价格政策下一个阶梯的最少总数量要大于等于当前的数量  - 阶梯最少总数量要大于上一个阶梯的最少总数量,请修改
     */
    String SFA_SIMPLE_PRICE_POLICY_NEXT_LADDER_MINTOTALQUANTITY_MUST_GTE_CURRENT = "sfa.simple_price_policy.next.ladder.mintotalquantity.must.gte.current";
    /**
     * 简易价格政策条件行必选,最小数量不能为空   - 必填的条件行,最少数量不能为空,请输入
     */
    String SFA_SIMPLE_PRICE_POLICY_CONDITION_REQUIRE_MUST_NOT_EMPTY = "sfa.simple_price_policy.condition.require.must.not.empty";
    /**
     * 简易价格政策条件行,阶梯最小数量大于等于下一个梯最小数量  - 阶梯最少数量要大于上一个阶梯的最少数量,请修改
     */
    String SFA_SIMPLE_PRICE_POLICY_NEXT_LADDER_CONDITION_MINQUANTITY_MUST_GTE_CURRENT = "sfa.simple_price_policy.next.ladder.condition.minquantity.must.gte.current";
    /**
     * 简易价格政策阶梯内最少总数量大于等于必选最少数量之和    最少总数量要大于登入必选最少数量之和,请修改
     */
    String SFA_SIMPLE_PRICE_POLICY_LADDER_MINTOTALQUANTITY_MUST_GTE_REQUIRE_MINQUANTITY_SUM = "sfa.simple_price_policy.ladder.mintotalquantity.must.gte.require.minquantity.sum";

    /**
     * 您没有所选数据的读写权限或对象编辑功能权限，无权操作合并。
     */
    String SFA_MERGE_YOU_NOT_HAVE_READ_WRITE_PERMISSION = "sfa.merge.noprivilegeerror";
    /**
     * 找不到简易和通用版价格政策的bean  提示通用价格政策类型为比传项
     */
    String SFA_PRICEPOLICY_NOT_FIND_TYPE_SIMPLE_GENERAL = "sfa.pricepolicy.not.find.type.simple.general";
    /**
     * 价格政策 modetype不能为null
     */
    String SFA_PRICE_POLICY_MODETYPE_OUT_NULL = "sfa.price.policy.modetype.out.null";
    /**
     * 当前登入人没有布局
     */
    String SFA_PRICE_POLICY_LAYOUT_USER_NOT_HAVE = "sfa.price.policy.layout.user.not.have";
    /**
     * 组合不能为空
     */
    String SFA_GENERAL_SIMPLE_PRICE_POLICY_GROUPINGS_MUST_HAVE = "sfa.general.simple.price.policy.groupings.must.have";
    /**
     * 产品和分类不能为空
     */
    String SFA_SIMPLE_GENERAL_PRICE_POLICY_CATEGORY_PRODUCT_MUST_HAVE = "sfa.simple.general.price.policy.category.product.must.have";
    /**
     * 买赠单品促,多组合只能有一个合计满
     */
    String SFA_SIMPLE_GENERAL_PRICE_POLICY_DETAILS_GIFT_ALL_ONLY_ONE = "sfa.simple.general.price.policy.details.gift.all.only.one";
    /**
     * 通用价格正常分类和产品不能为空
     */
    String SFA_SIMPLE_GENERAL_PRICE_PRODUCT_CATEGORY_MUST_HAVE = "sfa.simple.general.price.product.category.must.have";
    /**
     * 通用价格政策通过名称不能获取处理器
     */
    String SFA_SIMPLE_GENERAL_PRICE_POLICY_HANDLLER_MUST_HAVE = "sfa.simple.general.price.policy.handller.must.have";
    /**
     * 通用价格政策,单品满赠创建聚合规则为空
     */
    String SFA_SIMPLE_GENERAL_PRICE_POLICY_AGG_MUST_HAVE = "sfa.simple.general.price.policy.agg.must.have";

    /**
     * 订单没有价格政策，但是【促销优惠额】不为零
     */
    String SFA_PRICE_POLICY_CHECK_POLICY_DYNAMIC_AMOUNT_NOT_ZERO = "sfa.price.policy.policy.dynamic.amount.not.zero";

    /**
     * 订单产品:%s【促销优惠额】不为零，但是没有价格政策
     */
    String SFA_PRICE_POLICY_CHECK_HAVE_POLICY_DYNAMIC_AMOUNT_NOT_POLICY = "sfa.price.policy.have.policy.dynamic.amount.not.policy";

    /**
     * 订单产品:{0}【促销分摊金额】不等于【促销优惠额】
     */
    String SFA_PRICE_POLICY_CHECK_POLICY_DYNAMIC_AMOUNT_NEQ_AMORTIZE_AMOUNT = "sfa.price.policy.check.policy.dynamic.amount.neq.amortize.amount";

    /**
     * 订单产品:{0}是赠品，但是【促销后小计】不为零
     */
    String SFA_PRICE_POLICY_CHECK_GIVEAWAY_POLICY_DYNAMIC_AMOUNT_NEQ_ZERO = "sfa.price.policy.check.giveaway.policy.dynamic.amount.neq.zero";

    /**
     * 订单产品:{0}使用的是金额类促销规则，但是【促销优惠额】为零
     */
    String SFA_PRICE_POLICY_CHECK_PRICING_RULE_DYNAMIC_AMOUNT_EQ_ZERO = "sfa.price.policy.check.pricing.rule.dynamic.amount.eq.zero";
    /**
     * 未满足每满条件，但是匹配到了赠品
     */
    String SFA_PRICE_POLICY_CHECK_MATCHED_GIFT = "sfa.price.policy.check.matched.gift";

    /**
     * {0}【赠品分摊价格】不正确
     */
    String SFA_PRICE_POLICY_CHECK_GIFT_AMORTIZE = "sfa.price.policy.check.gift.amortize";

    /**
     * 赠品种类超过赠品限制
     */
    String SFA_PRICE_POLICY_GIFT_TYPE_LIMIT = "sfa.price.policy.gift.type.limit";
    /**
     * 赠品数量超过赠品总数量限制
     */
    String SFA_PRICE_POLICY_GIFT_TOTAL_LIMIT = "sfa.price.policy.gift.total.limit";
    /**
     * {0}为必选赠品，但没有赠送
     */
    String SFA_PRICE_POLICY_GIFT_MUST_HAVE = "sfa.price.policy.gift.must.have";

    /**
     * {0}赠送数量超过最大数量限制
     */
    String SFA_PRICE_POLICY_GIFT_TOTAL_MORE_THEN_MAX_LIMIT = "sfa.price.policy.gift.total.more.then.max.limit";

    /**
     * {0}赠送数量小于最小数量限制
     */
    String SFA_PRICE_POLICY_GIFT_TOTAL_LESS_THEN_MAX_LIMIT = "sfa.price.policy.gift.total.less.then.max.limit";

    /**
     * 赠送了不在赠品范围内的赠品，请检查
     */
    String SFA_PRICE_POLICY_ORDER_PRODUCT_GIFT_NOT_IN_POLICY_GIFT = "sfa.price.policy.order.product.gift.not.in.policy.gift";

    /**
     * 缺少固定赠品{0}
     */
    String SFA_PRICE_POLICY_GIFT_LOSE = "sfa.price.policy.gift.lose";

    /**
     * 固定赠品{0}数量不正确，应为:{1}
     */
    String SFA_PRICE_POLICY_GIFT_TOTAL_ERROR = "sfa.price.policy.gift.total.error";

    /**
     * 固定赠品{0}单位不正确
     */
    String SFA_PRICE_POLICY_GIFT_UNIT_ERROR = "sfa.price.policy.gift.unit.error";

    /**
     * 可选赠品{0}单位不正确
     */
    String SFA_PRICE_POLICY_CHOSE_GIFT_UNIT_ERROR = "sfa.price.policy.chose.gift.unit.error";


    /**
     * 可选赠品指定范围赠送单位不正确
     */
    String SFA_PRICE_POLICY_CHOSE_CONDITION_GIFT_UNIT_ERROR = "sfa.price.policy.chose.condition.gift.unit.error";

    /**
     * {0}不在赠品范围内，请重新检查
     */
    String SFA_PRICE_POLICY_GIFT_NOT_MATCH_CONDITION = "sfa.price.policy.gift.not.match.condition";

    /**
     * 当前客户不适用价格政策：{0}！
     */
    String SFA_PRICE_POLICY_NOT_ACCEPT_ACCOUNT = "sfa.price.policy.not.accept.account";


    /**
     * 关键词数量超出 {0} 限制
     */
    String SFA_PROCUREMENT_RULE_KEYWORDS_EXCEEDED_LIMIT = "sfa.procurement.rule.keywords.exceeded.limit";

    /**
     * 订单整单分摊政策分摊行数据不正确！
     */
    String SFA_PRICE_POLICY_AMORTIZE_MONEY_ERROR = "sfa.price.policy.amortize.money.error";

    /**
     * 参数中传入的价格政策规则，没有传入对应的价格政策
     */
    String SFA_PRICE_POLICY_NOT_EXISTS_POLICY_ERROR = "sfa.price.policy.not.exists.policy.error";

    /**
     * 赠品没本品，请去掉赠品{0}
     */
    String SFA_PRICE_POLICY_GIFT_NOT_EXIST_PRODUCT = "sfa.price.policy.gift.not.exist.product";

    /**
     * 价格规则{0}修改量条件不满足！
     */
    String SFA_PRICE_RULE_MODIFY_CONDITION_NOT_MATCH = "sfa.price.rule.modify.condition.not.match";

    /**
     * 组合规则{0}条件不满足！
     */
    String SFA_PRICE_GROUP_RULE_CONDITION_NOT_MATCH = "sfa.price.group.rule.condition.not.match";

    /**
     * 使用了不满足规则条件的价格政策！
     */
    String SFA_PRICE_POLICY_CONDITION_NOT_MATCH = "sfa.price.policy.condition.not.match";

    /**
     * 价格规则{0}条件不满足！
     */
    String SFA_PRICE_RULE_CONDITION_NOT_MATCH = "sfa.price.rule.condition.not.match";
    /**
     * 使用了不可使用得价格规则！
     */
    String SFA_PRICE_RULE_CAN_NOT_USE = "sfa.price.rule.can.not.use";
    /**
     * 使用了不可使用得价格政策！
     */
    String SFA_PRICE_POLICY_CAN_NOT_USE = "sfa.price.policy.can.not.use";

    /**
     * 价格政策：{0}已经禁用！
     */
    String SFA_PRICE_POLICY_DISABLED = "sfa.price.policy.disabled";


    /**
     * 价格政策：{0}已经超过有效期！
     */
    String SFA_PRICE_POLICY_EXPIRY_DATE = "sfa.price.policy.expiry.date";

    /**
     * 价格政策：{0}已经限制客户使用！
     */
    String SFA_PRICE_POLICY_LIMIT_ACCOUNT_USED = "sfa.price.policy.limit.account.used";


    /**
     * 招投标市场分析 数量已达到上限 {0}
     */
    String SFA_PROCUREMENT_RULE_COUNT_REACH_LIMITED = "sfa.procurement.rule.count.reach.limited";


    /**
     * 规则数量已达到上限: {0}
     */
    String SFA_BIDDING_RULE_COUNT_REACH_LIMITED = "sfa.bidding.rule.count.reach.limited";

    /**
     * 关键词数量超过上限:
     */
    String SFA_BIDDING_QLM_KEYWORDS_COUNT_LIMITED = "sfa.bidding.qlm.keywords.count.limited";

    /**
     * 关键词关系超过上限:
     */
    String SFA_BIDDING_QLM_KEYWORDS_RELATIONSHIP_LIMITED = "sfa.bidding.qlm.keywords.relationship.limited";

    /**
     * 关键词为空
     */
    String SFA_BIDDING_KEYWORDS_IS_EMPTY = "sfa.bidding.keywords.is.empty";

    /**
     * 关键词格式错误
     */
    String SFA_BIDDING_KEYWORDS_FORMAT_ERR = "sfa.bidding.keywords.format.err";

    /**
     * 剩余配额不足，请联系您的销售经理购买数据包。
     */
    String SFA_IMPORT_RULE_LICENSE_NOT_ENOUGH = "sfa.import.rule.license.not.enough";


    /**
     * 数据量已达到上限: {0}，请适当调整订阅条件。
     */
    String SFA_IMPORT_RULE_TASK_COUNT_REACH_LIMITED = "sfa.import.rule.task.count.reach.limited";


    /**
     * 已完成状态不可启用。
     */
    String SFA_IMPORT_RULE_TASK_FINISHED_CANNOT_ENABLE = "sfa.import.rule.task.finished.cannot.enable";

    /**
     * 已暂停状态不可启用。
     */
    String SFA_IMPORT_RULE_TASK_SUSPEND_CANNOT_DISABLE = "sfa.import.rule.task.suspend.cannot.disable";

    /**
     * 进行中状态不可停用。
     */
    String SFA_IMPORT_RULE_TASK_RUNNING_CANNOT_DISABLED = "sfa.import.rule.task.running.cannot.disabled";

    /**
     * 有任务在进行中不可启用。
     */
    String SFA_IMPORT_RULE_TASK_CANNOT_ENABLE = "sfa.import.rule.task.cannot.enable";

    /**
     * I18N.text("sfa.bidding.subscription.mobile.not.support")/*很抱歉，目前移动端暂不支持此功能。我们正在开发中，敬请期待
     */
    String SFA_BIDDING_SUBSCRIPTING_MOBILE_NOT_SUPPORT = "sfa.bidding.subscription.mobile.not.support";

    /**
     * 招投标市场分析 数量已经达到上限{0}，{1}条分析结果待生成
     */
    String SFA_PROCUREMENT_ANALYSIS_DISABLED_COUNT_REACH_LIMITED = "sfa.procurement.analysis.disabled.count.reach.limited";

    /**
     * 【R\F\M值重复，无法保存】！
     */
    String SFA_RFM_SCORE_HAVE_DUPLICATE = "sfa.rfm.score.have.duplicate";

    /**
     * 规则：{0}每满条件不满足！
     */
    String SFA_PRICE_POLICY_RULE_CYCLE_NOT_MATCH = "sfa.price.policy.rule.cycle.not.match";

    /**
     * 规则：{0}优惠金额超出限制！
     */
    String SFA_PRICE_POLICY_RULE_USE_MONEY_OVER = "sfa.price.policy.rule.use.money.over";
    /**
     * 简易价格政策产品/分类选择过多
     */
    String SFA_SIMPLE_PRICE_POLICY_CONDITION_TOO_MANY = "sfa.simple.price.policy.condition.too.many";
    /**
     * 简易价格政策产品/分类选择必选项过多
     */
    String SFA_SIMPLE_PRICE_POLICY_CONDITION_REQUIRE_TOO_MANY = "sfa.simple.price.policy.condition.required.too.many";


    /**
     * 您的规则量已达上限（最多{0}个对象，{1}个规则/对象），无法继续新建。
     */
    String SFA_RFM_RULE_LIMIT_NUM = "sfa.rfm.rule.limit.num";
    /**
     * 新
     */
    String RFMRULEOBJ_UPDATE_NEW = "RFMRuleObj.update.new";
    /**
     * 原
     */
    String RFMRULEOBJ_UPDATE_OLD = "RFMRuleObj.update.old";

    /**
     * 导入更新id重复
     */
    String SFA_UPDATE_IMPORT_ID_DUPLICATE = "sfa.update.import.id.duplicate";

    /**
     * 参数错误！
     */
    String SFA_CONFIG_PARAMETER_ERROR1 = "sfa.config.parametererror1";
    /**
     * 您未开启合作伙伴开关，请在后台合作伙伴管理中，开启合作伙伴后再次添加对象。
     */
    String SFA_PARTNER_SWITCH_NOT_OPEN = "sfa.partner.switch.not.open";

    /**
     * {0}市场分析订阅设置：{1}
     * 启用市场分析订阅设置：设置1
     * 禁用市场分析订阅设置：设置1
     * 删除市场分析订阅设置：设置1
     */
    String SFA_PROCUREMENT_RULE_OP_LOG_MSG = "sfa.procurement.rule.op.log.msg";


    /**
     * 开通会话存档且绑定企微账号的员工方可查看企微会话内容
     */
    String SFA_PARAM_UNBOUND_USER_ID = "sfa.param.unbound.user.id";
    /**
     * {0} 中的必填字段 {1} 未设置映射规则，无法执行批量转存操作。
     */
    String SFA_PROCUREMENT_MAPPING_RULE_REQUIRED_IS_NULL = "sfa.procurement.mapping.rule.required.is.null";

    /**
     * 勾选
     */
    String SFA_FOLLOW_DEAL_SETTING_CHECK_LOG = "sfa.follow.deal.setting.check.log";
    /**
     * 取消勾选
     */
    String SFA_FOLLOW_DEAL_SETTING_UNCHECK_LOG = "sfa.follow.deal.setting.uncheck.log";
    /**
     * 客户跟进规则
     */
    String SFA_ACCOUNT_OBJ_FOLLOW_DEAL_SETTING = "sfa.account.obj.follow.deal.setting";

    /**
     * 线索跟进规则
     */
    String SFA_LEADS_OBJ_FOLLOW_DEAL_SETTING = "sfa.leads.obj.follow.deal.setting";

    /**
     * 当前时间暂不允许该操作
     */
    String SFA_PROCUREMENT_MAPPING_RULE_TIME_ERROR = "sfa.procurement.mapping.rule.time.error";

    /**
     * 所选数据超过最大限制
     */
    String SFA_PROCUREMENT_REACHED_MAX_LIMIT_SIZE = "sfa.procurement.reached.max.limit.size";

    /**
     * 计算范围为按历史累计值返，不支持按交易明细返利
     */
    String SFA_REBATE_POLICY_NOT_SUPPORT_HISTORY_DETAIL = "sfa.rebate.policy.not.support.history.detail";

    /**
     * 计算范围为按历史累计值返，不支持逐单产生
     */
    String SFA_REBATE_POLICY_NOT_SUPPORT_HISTORY_EACH = "sfa.rebate.policy.not.support.history.each";

    /**
     * 计算范围为按当单值返，只支持逐单产生
     */
    String SFA_REBATE_POLICY_SUPPORT_CURRENT_EACH = "sfa.rebate.policy.support.current.each";

    /**
     * 返利产生政策适用客户，最多支持200个客户
     */
    String SFA_REBATE_POLICY_SUPPORT_FIXED_ACCOUNT_TOTAL = "sfa.rebate.policy.support.fixed.account.total";

    /**
     * 按历史累计值返，单据条件只能有聚合规则
     */
    String SFA_REBATE_POLICY_HISTORY_ONLY_AGGREGATE_RULE = "sfa.rebate.policy.history.only.aggregate.rule";

    /**
     * 按历史累计值返，不能有产品条件
     */
    String SFA_REBATE_POLICY_HISTORY_NOT_HISTORY_RULE = "sfa.rebate.policy.history.not.history.rule";

    /**
     * 产品条件不可以为空
     */
    String SFA_REBATE_POLICY_EXECUTION_CONDITION_NOT_NULL = "sfa.rebate.policy.execution.condition.not.null";
    /**
     * 条件函数信息不完整
     */
    String SFA_REBATE_POLICY_CONDITION_FUNCTION_NOT_NULL = "sfa.rebate.policy.condition.function.not.null";
    /**
     * 执行函数信息不完整
     */
    String SFA_REBATE_POLICY_EXECUTION_FUNCTION_NOT_NULL = "sfa.rebate.policy.execution.function.not.null";
    /**
     * 改变结果函数信息不完整
     */
    String SFA_REBATE_POLICY_CHANGE_RESULT_FUNCTION_NOT_NULL = "sfa.rebate.policy.change.result.function.not.null";
    /**
     * {0}函数命名空间不匹配
     */
    String SFA_REBATE_POLICY_FUNCTION_NAMESPACE_NOT_MATCH = "sfa.rebate.policy.function.namespace.not.match";

    /**
     * {0}函数不存在
     */
    String SFA_REBATE_POLICY_FUNCTION_NAMESPACE_NOT_EXIST = "sfa.rebate.policy.function.namespace.not.exist";

    /**
     * 函数命名空间未查询到函数结果
     */
    String SFA_REBATE_POLICY_FUNCTION_NAMESPACE_NOT_RESULT = "sfa.rebate.policy.function.namespace.not.result";

    /**
     * 客户成交规则
     */
    String SFA_ACCOUNT_OBJ_DEAL_RULE = "sfa.account.obj.deal.rule";

    /**
     * 预置成交规则
     */
    String SFA_ACCOUNT_OBJ_DEAL_RULE_PRESET = "sfa.account.obj.deal.rule.preset";

    /**
     * 自定义成交规则
     */
    String SFA_ACCOUNT_OBJ_DEAL_RULE_CUSTOMIZE = "sfa.account.obj.deal.rule.customize";


    /**
     * 转存操作正在处理中，请稍后再试
     */
    String SFA_PROCUREMENT_TRANSFER_IS_PROCESSING = "sfa.procurement.transfer.is.processing";

    /**
     * 参数{0}不可以为空
     */
    String SFA_PARAM_BLANK_CHECK_MSG = "sfa.param.blank.check.msg";
    /**
     * 参数{0}不符合协议
     */
    String SFA_PARAM_ERROR = "sfa.param.error";

    /**
     * 对象:{0} 不支持层级
     */
    String SFA_OBJECT_NOT_SUPPORT_TREE_PATH = "sfa.object.not.support.tree.path";

    /**
     * 客户成交规则：原 {0}，现 {1}
     */
    String SFA_ACCOUNT_OBJ_DEAL_RULE_CHANGE_MSG = "sfa.account.obj.deal.rule.change.msg";

    /**
     * 客户成交规则-预置成交规则变更：原 {0}，现 {1}
     */
    String SFA_ACCOUNT_OBJ_PRESET_DEAL_RULE_CHANGE_MSG = "sfa.account.obj.preset.deal.rule.change.msg";

    /**
     * 客户有促销，请尝试使用促销。
     */
    String SFA_PRICE_POLICY_CHECK_NOT_MATCH = "sfa.price.policy.check.not.match";

    /**
     * 客户有匹配到的促销未使用。
     */
    String SFA_PRICE_POLICY_CHECK_NOT_USE_MATCH_DATA = "sfa.price.policy.check.not.use.match.data";

    /**
     * 原币币种，必须和订单保持一致
     */
    String SFA_ORIGINAL_CURRENCY_SAME_AS_ORDER = "sfa.original.currency.same.as.order";

    /**
     * 本位币币种，必须和订单保持一致
     */
    String SFA_BASE_CURRENCY_SAME_AS_ORDER = "sfa.base.currency.same.as.order";

    /**
     * 汇率，必须和订单保持一致
     */
    String SFA_CONVERSION_RATE_SAME_AS_ORDER = "sfa.conversion.rate.same.as.order";

    /**
     * 现在已经开启开票2.0的企业过多，请稍后再试！
     */
    String SFA_TOO_MANY_TENANTS_OPENING_INVOICE_MODE2_NOW = "sfa.too.many.tenants.opening.invoice.mode2.now";

    /**
     * 不能直接切换到订单产品开票模式！
     */
    String SFA_CAN_NOT_SWITCH_ORDER_PRODUCT_INVOICE_MODE = "sfa.can.not.switch.order.product.invoice.mode";

    /**
     * 订单的整单折扣被禁用或删除，不允许开启新开票，请启用。
     */
    String SFA_ORDER_DISCOUNT_DISABLE_NOT_OPEN_INVOICE = "sfa.order.discount.disable.not.open.invoice";

    /**
     * 配置开启中，请勿重复操作！
     */
    String SFA_INVOICE_CONFIG_IS_OPENING = "sfa.invoice.config.is.opening";

    /**
     * 开启成功后，不能进行操作！
     */
    String SFA_INVOICE_CONFIG_HAS_OPENED = "sfa.invoice.config.has.opened";

    /**
     * 开启配置项失败
     */
    String SFA_CONFIG_OPEN_ERROR = "sfa.config.open.error";

    /**
     * 该功能依赖新开票的开启
     */
    String SFA_FUNC_DEPEND_OPEN_NEW_INVOICE = "sfa.func.depend.open.new.invoice";

    /**
     * 正在迁移数据，请稍等。
     */
    String SFA_DATA_IS_TRANSFER = "sfa.data.is.transfer";

    /**
     * 数据较多，已经创建任务晚上迁移，请明天使用。
     */
    String SFA_TOO_MANY_DATA_TO_TRANSFER_AT_NIGHT = "sfa.too.many.data.to.transfer.at.night";

    /**
     * 非标属性关联产品，无法删除
     */
    String SFA_PRODUCT_RELATED_NONSTANDARD_ATTRIBUTE = "sfa.product.related.nonstandard.attribute";

    /**
     * 非标属性关联上限50个
     */
    String SFA_PRODUCT_RELATED_NONSTANDARD_ATTRIBUTE_LIMIT = "sfa.product.related.nonstandard.attribute.limit";
    /**
     * {0}，开票金额超出最大可开票金额
     */
    String SFA_INVOICED_AMOUNT_MORE_THAN_MAX_AMOUNT = "sfa.invoiced.amount.more.than.max.amount";

    /**
     * 增量更新不能更换销售流程
     */
    String SFA_INCREMENTAL_UPDATES_CANNOT_REPLACE_SALES_PROCESS = "sfa.incremental.updates.cannot.replace.sales.process";

    /**
     * 销售流程和阶段不匹配，请刷新重试
     */
    String SFA_SALES_PROCESS_AND_STAGE_DO_NOT_MATCH = "sfa.sales.process.and.stage.do.not.match";

    /**
     * 请填写商机阶段
     */
    String SFA_FILL_IN_SALES_STAGE = "sfa.fill.in.sales.stage";

    /**
     * 结单时间转换失败
     */
    String SFA_NEWOPPORTUNITY_VALIDATOR_CLOSE_DATE_PARSE_FAILED = "sfa.newopportunity.validator.close.date.parse.failed";

    /**
     * 上下架时间转换失败
     */
    String SFA_NPRODUCT_VALIDATOR_DATE_PARSE_FAILED = "sfa.product.validator.date.parse.failed";

    /**
     * 调用阶段推进器接口解析异常
     */
    String SFA_NEWOPPORTUNITY_VALIDATOR_STAGES_PROCESSOR_PARSE_FAILED = "sfa.newopportunity.validator.stages.processor.parse.failed";

    /**
     * 该阶段不在所在商机流程中
     */
    String SFA_NEWOPPORTUNITY_VALIDATOR_STAGES_IS_EMPTY = "sfa.newopportunity.validator.stages.is.empty";

    /**
     * 阶段推进器解析extraDataModel异常
     */
    String SFA_NEWOPPORTUNITY_VALIDATOR_STAGES_PROCESSOR_EXTRA_DATA_PARSE_FAILED = "sfa.newopportunity.validator.stages.processor.extra.data.parse.failed";

    /**
     * 阶段推进器获取赢率异常
     */
    String SFA_NEWOPPORTUNITY_VALIDATOR_STAGES_PROCESSOR_PROBABILITY_PARSE_FAILED = "sfa.newopportunity.validator.stages.processor.probability.parse.failed";
    /**
     * 回收规则不能为空
     */
    String SFA_RECYCLING_RULE_NOT_NULL = "sfa.recycling.rule.not.null";
    /**
     * 回收规则部门不能为空
     */
    String SFA_RECYCLING_RULE_DEPARTMENT_NOT_NULL = "sfa.recycling.rule.department.not.null";
    /**
     * 请设置回收公海
     */
    String SFA_PLEASE_SET_RECYCLING_HIGHSEAS = "sfa.please.set.recycling.highseas";
    /**
     * 至少设置一个回收天数规则！
     */
    String SFA_SET_AT_LEAST_ONE_RECOVERY_DAYS_RULE = "sfa.set.at.least.one.recovery.days.rule";
    /**
     * 保有量规则超过最大数量50
     */
    String SFA_INVENTORY_RULE_EXCEEDS_THE_MAXIMUM_QUANTITY_OF_50 = "sfa.Inventory.rule.exceeds.the.maximum.quantity.of.50";
    /**
     * 保有量规则: %s 已经存在
     */
    String SFA_INVENTORY_RULE_IS_HAVE = "sfa.Inventory.rule.is.have";
    /**
     * 保有量规则名称不能为空
     */
    String SFA_INVENTORY_RULE_NAME_NOT_NULL = "sfa.Inventory.rule.name.not.null";
    /**
     * 保有量规则名称长度不能超过100
     */
    String SFA_INVENTORY_RULE_NAME_NOT_OUTSTRIP_100 = "sfa.Inventory.rule.name.not.outstrip.100";
    /**
     * 保有量适用范围不能为空
     */
    String SFA_INVENTORY_RULE_APPLICABLE_RANGE_CANNOT_BE_EMPTY = "sfa.Inventory.rule.Applicable.range.cannot.be.empty";
    /**
     * 保有量适用范围筛选条件设置错误
     */
    String SFA_INVENTORY_RULE_APPLICABLE_RANGE_WHERE_SET_ERROR = "sfa.Inventory.rule.Applicable.range.where.set.error";
    /**
     * 保有量筛选条件设置错误
     */
    String SFA_INVENTORY_RULE_WHERE_SET_ERROR = "sfa.Inventory.rule.where.set.error";
    /**
     * 保有量适用范围设置错误
     */
    String SFA_INVENTORY_RULE_APPLICABLE_RANGE_SET_ERROR = "sfa.Inventory.rule.set.error";
    /**
     * 用户Id有误!
     */
    String SFA_USER_ID_ERROR = "sfa.user.id.error";
    /**
     * key错误!
     */
    String SFA_KEY_ERROR = "sfa.key.error";
    /**
     * 该主数据对象配置不存在
     */
    String SFA_MASTER_DATA_OBJECT_CONFIGURATION_DOES_NOT_EXIST = "sfa.master.data.object.configuration.does.not.exist";
    /**
     * 数据与对象apiName不匹配
     */
    String SFA_DATA_DOES_NOT_MATCH_OBJECT_APINAME = "sfa.Data.does.not.match.object.apiname";
    /**
     * 不支持的采集方式
     */
    String SFA_UNSUPPORTED_COLLECTION_METHOD = "sfa.Unsupported.collection.method";
    /**
     * 类型错误 is empty
     */
    String SFA_TYPE_ERROR_IS_EMPTY = "sfa.type.error.is.empty";
    /**
     * 待强刷数据超过100条, 无法处理
     */
    String SFA_MORE_THAN_100_PIECES_OF_DATA_TO_BRUSHED = "sfa.more.than.100.pieces.of.data.to.brushed";
    /**
     * 运行超时
     */
    String SFA_RUN_TIMEOUT = "sfa.Run.timeout";
    /**
     * 业务类型不存在
     */
    String SFA_BUSINESS_TYPE_DOES_NOT_EXIST = "sfa.Business.type.does.not.exist";
    /**
     * 业务类型[%s]已被禁用
     */
    String SFA_BUSINESS_TYPE_DISABLED = "sfa.Business.type.Disabled";
    /**
     * 获取字段描述失败
     */
    String SFA_FAILED_TO_GET_FIELD_DESCRIPTION = "sfa.Failed.to.get.field.description";

    /**
     * 延期天数超过上限，最大值9999
     */
    String SFA_EXTEND_EXPIRE_TIME_LENGTH_LIMITED = "sfa.extend.expire.time.length.limited";


    /**
     * 重复领取
     */
    String SFA_DUPLICATE_SEARCH_DUPLICATE_ALLOCATE = "sfa.procurement.search.duplicate_allocate";

    /**
     * 订单产品:{0}上价目表明细与价目表价格参数缺失异常
     */
    String SFA_SALES_ORDER_PRODUCT_PRICE_ID_PARAM_MISSING = "sfa.sales.order.product.price.id.param.missing";

    /**
     * 订单产品:{0}的销售价格，数量与小计之间的计算结果不正确
     */
    String SFA_SALES_ORDER_PRODUCT_PRICE_CALCULATE_INCORRECT = "sfa.sales.order.product.price.calculate.incorrect";

    /**
     * 返利单所属组织与客户所属组织不同
     */
    String SFA_REBATE_ORG_NOT_EQ_ACCOUNT = "sfa.rebate.org.not.eq.account";

    /**
     * 优惠券所属组织与客户所属组织不同
     */
    String SFA_COUPON_ORG_NOT_EQ_ACCOUNT = "sfa.coupon.org.not.eq.account";

    /**
     * 优惠券所属组织与方案所属组织不同
     */
    String SFA_COUPON_ORG_NOT_EQ_PLAN = "sfa.coupon.org.not.eq.plan";
    /**
     * 转
     */
    String SFA_TURN = "sfa.turn";
    /**
     * 没有变更为主地址的功能权限或没有客户地址的编辑权限
     */
    String SFA_ACCOUNT_ADDR_SETMAIN_CHECK_POWER = "sfa.accountAddr.setmain.check.power";

    /**
     * 没有变更为默认地址的功能权限或没有客户地址的编辑权限
     */
    String SFA_ACCOUNT_ADDR_SET_DEFAULT_CHECK_POWER = "sfa.accountAddr.setDefault.check.power";

    /**
     * 没有变更为默认财务的功能权限
     */
    String SFA_ACCOUNT_FININFO_SET_DEFAULT_CHECK_POWER = "sfa.accountFinInfo.setDefault.check.power";

    /**
     * 您暂无代理通应用，请购买代理通应用后再开启合作伙伴地址功能
     */
    String SAF_PRM_APP_NOT_EXISTS = "sfa.prm.app.not.exists";
    /**
     * 您暂未开启合作伙伴，请开启合作伙伴后再开启合作伙伴地址功能
     */
    String SAF_PARTNER_DO_NOT_OPEN = "sfa.partner.do.not.open";
    /**
     * 数据错误，请重新提交订单
     */
    String SFA_PRICE_POLICY_VALIDATOR_DATA_ERROR_TRY_AGAIN = "sfa.price.policy.validator.data.error.try.again";
    /**
     * 不允许取消勾选
     */
    String SFA_REBATE_POLICY_SOURCE_CONFIG_NOT_ALLOW_CANCEL_ITEM = "sfa.rebate.policy.source.config.not.allow.cancel.item";
    /**
     * 选中状态下，从对象已经有值，不允许修改从对象的值
     */
    String SFA_REBATE_POLICY_SOURCE_CONFIG_NOT_MODIFY_DETAIL = "sfa.rebate.policy.source.config.not.modify.detail";
    /**
     * 选中状态下，可选聚合维度已经有值，不允许修改可选聚合维度的值
     */
    String SFA_REBATE_POLICY_SOURCE_CONFIG_NOT_MODIFY_ACCOUNT = "sfa.rebate.policy.source.config.not.modify.account";
    /**
     * 主对象不能为空
     */
    String SFA_REBATE_POLICY_SOURCE_CONFIG_API_NAME_NOT_EMPTY = "sfa.rebate.policy.source.config.api.name.not.empty";
    /**
     * 从对象名称不能为空
     */
    String SFA_REBATE_POLICY_SOURCE_CONFIG_DETAIL_API_NAME_NOT_EMPTY = "sfa.rebate.policy.source.config.detail.api.name.not.empty";
    /**
     * 可聚合维度不能为空
     */
    String SFA_REBATE_POLICY_SOURCE_CONFIG_DIMENSION_NOT_EMPTY = "sfa.rebate.policy.source.config.dimension.not.empty";
    /**
     * 从对象中没有master_detail类型
     */
    String SFA_REBATE_POLICY_SOURCE_CONFIG_NOT_HAVE_MASTER_DETAIL = "sfa.rebate.policy.source.config.not.have.master.detail";
    /**
     * {0}从对象描述中的主对象，和配置的不一致
     */
    String SFA_REBATE_POLICY_SOURCE_CONFIG_DETAIL_OBJECT_NOT_INCONSISTENT_MASTER = "sfa.rebate.policy.source.config.detail.object.not.inconsistent.master";
    /**
     * 不允许超过20个对象，请修改
     */
    String SFA_REBATE_POLICY_SOURCE_NO_MORE_THEN = "sfa.rebate.policy.source.no.more.then";
    /**
     * 主对象中不存在关联客户对象
     */
    String SFA_REBATE_POLICY_SOURCE_NO_ACCOUNT = "sfa.rebate.policy.source.no.account";
    /**
     * 类型不能为空
     */
    String SFA_REBATE_POLICY_SOURCE_CONFIG_TYPE_NOT_EMPTY = "sfa.rebate.policy.source.config.type.not.empty";

    /**
     * 预制对象不允许删除
     */
    String SFA_REBATE_SOURCE_CONFIG_NOT_ALLOW_DELETE = "sfa.rebate.source.config.not.allow.delete";
    /**
     * 对象已经勾选，不允许删除
     */
    String SFA_REBATE_CONFIG_NOT_ALLOW_DELETE_SELECTED = "sfa.rebate.config.not.allow.delete.selected";
    /**
     * 解析条件异常
     */
    String SFA_PRICE_POLIY_RULE_EXPORT_CONDITION_ERROR = "sfa.price.poliy.rule.export.condition.error";
    /**
     * 执行量解析异常
     */
    String SFA_PRICE_POLICY_EXPORT_EXECUTION_ERROR = "sfa.price.policy.export.execution.error";
    /**
     * 赠品数量不能为0
     */
    String SFA_PRICE_POLICY_ADD_GIFT_MAX_VALUE_NOT_EMPTY = "sfa.price.policy.add.gift.max.value.not.empty";
    /**
     * 价格政策明细限量为[%s]，已经有订单占用，不能删除！
     */
    String SFA_PRICE_POLICY_LIMIT_ACCOUNT_ASYNC_BULK_INVALID_ERROR = "sfa.price.policy.limit.account.async.bulk.invalid.error";
    /**
     * 无规格商品的产品不允许修改负责人
     */
    String SFA_PRODUCT_IMPORT_CHECK_SPEC = "sfa.product.import.check.spec";
    /**
     * 返利产生来源
     */
    String SFA_REBATE_SOURCE_DATA = "sfa.rebate.source.data";
    /**
     * 返利产生来源
     */
    String SFA_PRIMARY_ATTRIBUTE_FIELD_IS_NULL = "sfa.primary.attribute.field.is.null";
    /**
     * 每满条件最多支持{0}个
     */
    String PRICE_POLICY_CYCLE_INFO_SIZE_MUST_LESS_THAN_FIVE = "price_policy.cycle_info_size_must_less_than_five";
    /**
     * 每满规则中条件字段不能重复
     */
    String PRICE_POLICY_CYCLE_FIELD_NAME_MUST_BE_UNIQUE = "price_policy.cycle_field_name_must_be_unique";
    /**
     * 每满中组合规则，和条件中组合规则不一致
     */
    String SFA_PRICE_POLICY_CYCLE_INFO_DIFF_CONDITION_INFO = "sfa.price.policy.cycle.info.diff.condition.info";
    /**
     * 组合规则
     */
    String SFA_PRICE_POLICY_CYCLE_GROUP_DISPLAYNAME = "sfa.price.policy.cycle.group.displayname";

    /**
     * 对象: {0} 不支持添加到代理通中
     */
    String PRM_NOT_SUPPORTED_PRE_OBJETS = "prm.not.supported.pre.objets";
    /**
     * 组织名称：
     */
    String SFA_MAIN_CONTROL_ORG_LABEL = "sfa.main.control.org.label";
    /**
     * 更新为：
     */
    String SFA_MAIN_CONTROL_UPDATE_LABEL = "sfa.main.control.update.label";
    /**
     * 配置已经删除
     */
    String SFA_MAIN_CONTROL_CONFIG_DELETED_LABEL = "sfa.main.control.config.deleted.label";
    /**
     * 新建为：
     */
    String SFA_MAIN_CONTROL_ADD_LABEL = "sfa.main.control.add.label";
    /**
     * 字段名称：
     */
    String SFA_MAIN_CONTROL_FIELD_NAME_LABEL = "sfa.main.control.field.name.label";
    /**
     * 是否更新：
     */
    String SFA_MAIN_CONTROL_IS_UPDATE_LABEL = "sfa.main.control.is.update.label";
    /**
     * 更新方式：
     */
    String SFA_MAIN_CONTROL_UPDATE_TYPE_LABEL = "sfa.main.control.update.type.label";
    /**
     * 拼接符号：
     */
    String SFA_MAIN_CONTROL_SPLICING_SYMBOLS_LABEL = "sfa.main.control.splicing.symbols.label";
    /**
     * 存在重复客户，请删除后重试
     */
    String PRICE_POLICY_CUSTOMER_REPEAT = "price_policy.customer_repeat";
    /**
     * 存在重复产品，请删除后重试
     */
    String PRICE_POLICY_PRODUCT_REPEAT = "price_policy.product_repeat";

    /**
     * 开启返利系统需要开启客户帐户功能
     */
    String SFA_REBATE_OPEN_CHECK_CUSTOMER_ACCOUNT = "sfa.rebate.open.check.customer.account";
    /**
     * 返利系统启用，不允许修改
     */
    String SFA_REBATE_OPEN_NOT_ALLOW_MODIFY = "sfa.rebate.open.not.allow.modify";
    /**
     * 返利系统开启中，请勿重复操作
     */
    String SFA_REBATE_IS_BEING_ACTIVED = "sfa.rebate.is.being.actived";
    /**
     * 返利系统不允许关闭
     */
    String SFA_REBATE_NOT_ALLOW_CLOSE = "sfa.rebate.not.allow.close";

    /**
     * 开启返利系统需要开启客户帐户的账户授权功能
     */
    String SFA_OPEN_REBATE_MUST_OPEN_CUSTOMER_AUTH = "sfa.open.rebate.must.open.customer.auth";
    /**
     * 开启返利单失败，请联系管理员
     */
    String SFA_OPEN_REBATE_FAIL = "sfa.open.rebate.fail";
    /**
     * {0}不存在或已经被删除！
     */
    String SFA_DATA_NOT_NOT_EXIT = "sfa_data_not_not_exit";
    /**
     * 关联的{0}必须在销售合同客户范围内
     */
    String SFA_RELATION_OBJECT_IN_SALE_CONTRACT_ACCOUNT = "sfa_relation_object_in_sale_contract_account";
    /**
     * 不是基于取价服务模式的{0}不能关联{1}
     */
    String SFA_NO_REAL_PRICE_MODE_NOT_ASSOCIATE = "sfa_no_real_price_mode_not_associate";
    /**
     * {0}不能重复
     */
    String SFA_FIX_OBJECT_DATA_NOT_REPEAT = "sfa.fix.object.data.not_repeat";

    /**
     * 请先开启合作伙伴后再开启分子公司
     */
    String OPEN_PARTNER_AND_THEN_OPEN_SUBSIDIARY = "open.partner.and.then.open.subsidiary";
    /**
     * 存在同名业务类型:{0}
     */
    String OPEN_SUBSIDIARY_EXISTS_SAME_NAME_RECORD_TYPE = "open.subsidiary.exists.same.name.record.type";

    /**
     * 添加分子公司自定义菜单失败
     */
    String OPEN_SUBSIDIARY_ADD_MENU_FAILED = "open.subsidiary.add.menu.failed";

    /**
     * 存在同名菜单项:分子公司
     */
    String OPEN_SUBSIDIARY_EXISTS_SAME_NAME_MENU = "open.subsidiary.exists.same.name.menu";

    /**
     * 当前未开启价目表，不能开启该功能
     */
    String SFA_SALE_CONTRACT_MODE_OPEN_PRICEBOOK_ = "sfa.sale.contract.mode.open.pricebook";

    /**
     * 当前未开启合同约束，不能开启该功能
     */
    String SFA_SALE_CONTRACT_MODE_OPEN = "sfa.sale.contract.mode.open";

    /**
     * {0} 转换为 {1}。
     */
    String SFA_A_CONVERT_B = "sfa.a.convert.b";

    /**
     * {销售线索} {xxx} 转换为 {联系人} {xxx} {、} {合作伙伴} {xxx}。{勾选}{勾选}<br/>
     * {0} {1} 转换为 {2} {3} {4} {5} {6}。{7}{8}
     */
    String SFA_A_CONVERT_B_NEW = "sfa.a.convert.b.new";

    /**
     * 勾选了[销售线索的相关团队自动带入合作伙伴] 普通成员。
     */
    String SFA_PUT_TEAM_MEMBERS_INTO_PARTNER = "sfa.put.team.members.into.partner";
    /**
     * 勾选了[销售线索的销售记录自动带入合作伙伴]。
     */
    String SFA_COMBINE_CRM_FEED = "sfa.combine.crm.feed";

    /**
     * 约束模式为取价模式下不能新建销售合同明细
     */
    String SFA_CONSTRAINT_MODE_REAL_PRICE_NO_DETAIL = "sfa.constraint.mode.real.price.no.detail";
    /**
     * 已经开启此功能，无法关闭
     */
    String SFA_PRICE_POLICY_NOT_ALLOW_CLOSE = "sfa.price.policy.not.allow.close";
    /**
     * 未开启价格政策主开关，不能开启此功能
     */
    String SFA_PRICE_POLICY_NOT_OPEN_PRICE_POLICY = "sfa.price.policy.not.open.price.policy";
    /**
     * 未开启销售合同开关，不能开启此功能
     */
    String SFA_SALE_CONTRACT_NOT_OPEN_CONTRACT_MODE = "sfa.sale.contract.not.open.contract.mode";
    /**
     * 带有线索池的线索不支持新建导入触发审批流程
     */
    String SFA_LEADS_POOL_SUPPORT_APPROVALFLOW = "sfa.leads.pool.not.support.approvalflow";
    /**
     * 创建客户主数据失败，使用查重规则查询到有重复的客户主数据
     */
    String SFA_MAIN_DATA_DUPLICATE_TIP = "sfa.main.data.duplicate.tip";
    /**
     * 返利单已经入账，不能编辑返利类型或者使用类型
     */
    String SFA_REBATE_CANNOT_EDIT_FOR_ENTER_ACCOUNT = "sfa.rebate.cannot.edit.for.enter.account";
    /**
     * 对象描述信息不完整
     */
    String SFA_OPEN_PRICE_POLICY_DESCRIBE_NOT_HAVE = "sfa.open.price.policy.describe.not.have";
    /**
     * 此功能已经开启，请勿重提提交！
     */
    String SFA_PRICE_POLICY_ADD_FIELD_ENABLED = "sfa.price.policy.add.field.enabled";
    /**
     * sfa.open.rebate.must.open.customer.check:开启返利系统需要开启客户帐户的校验规则功能
     */
    String SFA_OPEN_REBATE_MUST_OPEN_CUSTOMER_CHECK = "sfa.open.rebate.must.open.customer.check";

    /**
     * 促销分摊已过期，请修改订单后保存。
     */
    String SFA_PRICE_POLICY_CHECK_NOT_MATCH_AMORTIZE_DATA = "sfa.price.policy.check.not.match.amortize.data";
    /**
     * sfa.price.policy.check.gift.line.total:订单产品的赠品行数量，和价格政策赠品：{0}行数量不符，请检查
     */
    String SFA_PRICE_POLICY_CHECK_GIFT_LINE_TOTAL = "sfa.price.policy.check.gift.line.total";


    /**
     * 按"全文"搜索仅限查询发布时间为近3个月的数据
     */
    String SFA_PROCUREMENT_SEARCH_FULL_TEXT_LIMIT = "sfa.procurement.search.full.text.limit";
    /**
     * 股权关系获取成功
     */
    String SFA_EQUITY_INFORMATION_QUERY_SUCCESS_TITLE = "sfa.equity.information.query.success.title";
    /**
     * {0}股权关系获取成功
     */
    String SFA_EQUITY_INFORMATION_QUERY_SUCCESS_CONTENT = "sfa.equity.information.query.success.content";

    /**
     * 对象{0}不存在
     */
    String SFA_OBJECT_DESCRIBE_NOT_EXISTS = "sfa.object.describe.not.exists";
    /**
     * 固定赠品【本品】没有指定的单位【{0}】，无法赠送，请确认政策中的赠品设置
     */
    String SFA_PRICE_POLICY_SAVE_VALIDATE_THIS_PRODUCT_NO_HAVA_UNIT_TYPE = "sfa.price.policy.save.validate.this.product.no.hava.unit.type";
    /**
     * 聚合对象中没有找到优惠券插件
     */
    String SFA_COUPON_PLUGIN_NOT_FOUND_IN_AGGREGATE_OBJECT = "sfa.coupon.plugin.not.found.in.aggregate.object";
    /**
     * 聚合结果类型为优惠券可用范围时，聚合字段必须为数量
     */
    String SFA_AGGREGATE_FIELD_MUST_QUANTITY = "sfa.aggregate.field.must.quantity";
    /**
     * 产品总数量不能小于0
     */
    String SFA_COUPON_RULE_CONDITION_LIMIT_TOTAL_CANNOT_LT_ZERO = "sfa.coupon.rule.condition.limit.total.cannot.lt.zero";
    /**
     * {0}为优惠券使用范围，不可以设置单位信息
     */
    String SFA_COUPON_RULE_CONDITION_AGGREGATE_RULE_UNIT_CANNOT_EMPTY = "sfa.coupon.rule.condition.aggregate.rule.unit.cannot.empty";
    /**
     * {0}查询不到对应的聚合规则，请重新选择！
     */
    String SFA_COUPON_HARD_NOT_FIND_AGGREGATE_RULE = "sfa.coupon.hard.not.find.aggregate.rule";
    /**
     * 名称为：{0}的优惠券可用范围重复，请重新选择
     */
    String SFA_COUPON_RULE_CONDITION_AGGREGATE_RULE_CANNOT_REPEAT = "sfa.coupon.rule.condition.aggregate.rule.cannot.repeat";
    /**
     * {0}关联关系不存在，请重新编辑此聚合规则，并保存
     */
    String SFA_COUPON_RULE_CONDITION_AGGREGATE_RULE_NOT_EXIST = "sfa.coupon.rule.condition.aggregate.rule.not.exist";
    /**
     * 名称为：{0}的聚合规则，关联的聚合规则不存在，请重新编辑此规则，并保存
     */
    String SFA_COUPON_RULE_CONDITION_RELATION_AGGREGATE_RULE_NOT_EXIST = "sfa.coupon.rule.condition.relation.aggregate.rule.not.exist";
    /**
     * 名称为：{0}的聚合规则的聚合结果类型不是优惠券使用范围
     */
    String SFA_COUPON_RULE_CONDITION_AGGREGATE_RULE_NOT_COUPON_RANGE = "sfa.coupon.rule.condition.aggregate.rule.not.coupon.range";
    /**
     * 根据{0}对象未获取到{1}插件
     */
    String SFA_AGGREGATE_RULE_NOT_FIND_PLUGIN = "sfa.aggregate.rule.not.find.plugin";
    /**
     * 优惠券
     */
    String SFA_COUPON_PLUGIN_LABEL = "sfa.coupon.plugin.label";
    /**
     * 赠品【{0}】，不在赠品范围内，请检查
     */
    String SFA_PRICE_POLICY_ORDER_PRODUCT_GIFT_NOT_IN_POLICY_GIFT_BY_NAME = "sfa.price.policy.order.product.gift.not.in.policy.gift.by.name";
    /**
     * {0}必含数量必填
     */
    String SFA_SIMPLE_PRICE_POLICY_REQUIRED_QUANTITY = "sfa.simple.price.policy.required.quantity";
    /**
     * {0}必含数量必须大于零
     */
    String SFA_SIMPLE_PRICE_POLICY_GT_ZERO = "sfa.simple.price.policy.gt.zero";

    /**
     * 客户：{0}，风险画像已开启。
     */
    String SFA_ACCOUNT_RISK_PORTRAIT_ENABLE = "sfa.account.risk.portrait.enable";

    /**
     * 客户：{0}，已被标记黑名单。
     */
    String SFA_ACCOUNT_BLACKLIST_MARK = "sfa.account.blacklist.mark";

    /**
     * 客户：{0}，已被标记黑名单。
     */
    String SFA_ACCOUNT_BLACKLIST_CANCEL = "sfa.account.blacklist.cancel";

    /**
     * 客户：{0}，风险监控已开启。
     */
    String SFA_ACCOUNT_RISK_MONITOR_ENABLE = "sfa.account.risk.monitor.enable";

    /**
     * 客户：{0}，风险监控已关闭。
     */
    String SFA_ACCOUNT_RISK_MONITOR_DISABLE = "sfa.account.risk.monitor.disable";
    /**
     * 您未开启价目表，不可勾选此选项，请开启价目表之后重新勾选
     */
    String SFA_DO_NOT_CHECK_BEFORE_OPENING_PRICE_BOOK = "sfa.do.not.check.before.opening.price.book";
    /**
     * 无法找到该下游企业所属的合作伙伴数据
     */
    String SFA_DO_NOT_FIND_PARTNER_OF_DOWN_STREAM = "sfa.do.not.find.partner.of.down.stream";

    /**
     * 角色不可为空
     */
    String SFA_PARTNER_MANAGEMENT_ROLE_NOT_EMPTY = "sfa.partner.management.role.not.empty";
    /**
     * 配置数据 id 不可以为空
     */
    String SFA_PARTNER_MANAGEMENT_CONFIG_DATA_ID_NOT_EMPTY = "sfa.partner.management.config.data.id.not.empty";
    /**
     * 角色不存在
     */
    String SFA_PARTNER_MANAGEMENT_ROLE_NOT_EXISTS = "sfa.partner.management.role.not.exists";

    /**
     * 指定角色失败
     */
    String SFA_PARTNER_MANAGEMENT_SPECIFY_ROLE_FAILED = "sfa.partner.management.specify.role.failed";

    /**
     * 未找到配置数据，请检查配置数据是否被作废或删除
     */
    String SFA_PARTNER_MANAGEMENT_DO_NOT_FIND_CONFIG_DATA = "sfa.partner.management.do.not.find.config.data";
    /**
     * 不支持的通知类型
     */
    String SFA_PARTNER_MANAGEMENT_CAN_NOT_SUPPORT_NOTIFICATION_TYPE = "sfa.partner.management.can.not.support.notification.type";
    /**
     * 数据存在异常，存在多条配置数据
     */
    String SFA_PARTNER_MANAGEMENT_DATA_NOT_ONLY_ONE_EXCEPTION = "sfa.partner.management.data.not.only.one.exception";

    /**
     * 该企业版本不支持开通合作伙伴
     */
    String SFA_PARTNER_OPEN_NOT_SUPPORT_VERSION = "sfa.partner.open.not.support.version";

    /**
     * 请求参数不完整
     */
    String SFA_TAIL_DIFF_PARAM_ERROR = "sfa.tail.diff.param.error";

    /**
     * 总行数不能超过{0}
     */
    String SFA_TAIL_DIFF_BEYOND_MAX_ROW = "sfa.tail.diff.beyond.max.row";
    /**
     * 聚合规则的聚合维度不一致
     */
    String SFA_REBATE_POLICY_AGGREGATE_DIMENSION_NOT_ONE = "sfa.rebate.policy.aggregate.dimension.not.one";
    /**
     * 该聚合规则已经被引用，不支持修改！
     */
    String SFA_AGGREGATE_RULE_HAS_REFERENCE_CANNOT_MODIFY = "sfa.aggregate.rule.has.reference.cannot.modify";
    /**
     * 返利产生政策的返利维度，与聚合规则：{0}聚合维度不一致，请修改
     */
    String SFA_REBATE_POLICY_DIMENSION_NOT_MATCH_AGGREGATE_DIMENSION = "sfa.rebate.policy.dimension.not.match.aggregate.dimension";
    /**
     * price_policy.gift_and_percentile_can_not_be_exist:按每满执行和按每满条件执行不可同时存在，请修改！
     */
    String PRICE_POLICY_GIFT_AND_PERCENTILE_CAN_NOT_BE_EXIST = "price_policy.gift_and_percentile_can_not_be_exist";
    /**
     * price.policy.percentile.only.support.one:按比例执行只支持一组条件
     */
    String PRICE_POLICY_PERCENTILE_ONLY_SUPPORT_ONE = "price.policy.percentile.only.support.one";
    /**
     * price.policy.percentile.value.must.be.positive:按比例执行的满值，必须大于0
     */
    String PRICE_POLICY_PERCENTILE_VALUE_MUST_BE_POSITIVE = "price.policy.percentile.value.must.be.positive";
    /**
     * 按比例执行中的组合规则，与条件中配置的不一致，请修改！
     */
    String PRICE_POLICY_PERCENTILE_GROUP_INCONSISTENT_CONDITION = "price.policy.percentile.group.inconsistent.condition";
    /**
     * sfa.price.policy.rule.progressive.gift.not.allowed:差额累进不支持按比例赠送
     */
    String SFA_PRICE_POLICY_RULE_PROGRESSIVE_GIFT_NOT_ALLOWED = "sfa.price.policy.rule.progressive.gift.not.allowed";
    /**
     * 单品规则不可以在比例依据中配置组合聚合规则，请修改！
     */
    String PRICE_POLICY_CANNOT_SET_COMBINATION_AGGREGATE_RULE = "price.policy.cannot.set.combination.aggregate.rule";
    /**
     * 组合规则，不可以在比例依据中配置，非组合聚合规则，请修改！
     */
    String PRICE_POLICY_CANNOT_SET_NON_COMBINATION_AGGREGATE_RULE = "price.policy.cannot.set.non.combination.aggregate.rule";
    /**
     * 所选择的决策链已建立关系，请选择其他数据
     */
    String SFA_OPPORTUNITY_DECISION_CHAIN_PARENTID_BINDING = "sfa_opportunity_decision_chain_parentid_binding";
    /**
     * 数量
     */
    String SFA_PRICE_POLICY_SAVE_CHECK_QUANTITY = "sfa.price.policy.save.check.quantity";
    /**
     * 金额
     */
    String SFA_PRICE_POLICY_SAVE_CHECK_AMOUNT = "sfa.price.policy.save.check.amount";
    /**
     * sfa.price.policy.gift.total.more.then.max.limit.two:{0} 赠送{1}超过最大限制,最大值为{2}
     */
    String SFA_PRICE_POLICY_GIFT_TOTAL_MORE_THEN_MAX_LIMIT_TWO = "sfa.price.policy.gift.total.more.then.max.limit.two";
    /**
     * sfa.price.policy.gift.total.less.then.max.limit.two:{0} 赠送{1}低于最小限制,最小值为{2}
     */
    String SFA_PRICE_POLICY_GIFT_TOTAL_LESS_THEN_MAX_LIMIT_TWO = "sfa.price.policy.gift.total.less.then.max.limit.two";
    /**
     * sfa.price.policy.gift.total.limit:赠品{0}，超过赠品总{1}限制
     */
    String SFA_PRICE_POLICY_GIFT_TOTAL_LIMIT_TWO = "sfa.price.policy.gift.total.limit.two";
    /**
     * sfa.price.policy.gift.total.error.two:固定赠品{0},{1}不正确，应为{2}
     */
    String SFA_PRICE_POLICY_GIFT_TOTAL_ERROR_TWO = "sfa.price.policy.gift.total.error.two";
    /**
     * 按每满条件执行
     */
    String SFA_PRICE_POLICY_RULE_CYCLE_EVERY_FULL_CONDITION = "sfa.price.policy.rule.cycle.every.full.condition";
    /**
     * 按比例执行
     */
    String SFA_PRICE_POLICY_RULE_PERCENTILE_EXEC = "sfa.price.policy.rule.percentile.exec";
    /**
     * sfa.price.policy.rule.full:满
     */
    String SFA_PRICE_POLICY_RULE_FULL = "sfa.price.policy.rule.full";
    /**
     * 赠送固定赠品
     */
    String SFA_PRICE_POLICY_GIVE_FIX_GIFT = "sfa.price.policy.give.fix.gift";
    /**
     * 赠送可选赠品
     */
    String SFA_PRICE_POLICY_GIVE_OPTIONAL_GIFT = "sfa.price.policy.give.optional.gift";
    /**
     * (按数量)
     */
    String SFA_PRICE_POLICY_RULE_GIVE_QUANTITY_GIFT = "sfa.price.policy.rule.give.quantity.gift";
    /**
     * (按金额)
     */
    String SFA_PRICE_POLICY_RULE_GIVE_AMOUNT_GIFT = "sfa.price.policy.rule.give.amount.gift";
    /**
     * 参考比例：
     */
    String SFA_PRICE_POLICY_REFERENCE_SCALE = "sfa.price.policy.reference.scale";
    /**
     * 赠品总额
     */
    String SFA_PRICE_POLICY_RULE_AMOUNT_GIFT = "sfa.price.policy.rule.amount.gift";

    /**
     * 产品分类对象没有对象化，无法添加到代理通，请申请产品分类对象化后重新尝试
     */
    String SFA_PRM_ADD_CATEGORY_OBJECT_ERROR = "sfa.prm.add.category.object.error";
    /**
     * is_er_enterprise = true 则提示"当前客户已绑定游客，禁止删除"
     */
    String SFA_IS_ER_ENTERPRISE_GIFT = "sfa.is.er.enterprise.gift";
    /**
     * is_er_enterprise = true 则提示"用户关联了互联企业，禁止作废"
     */
    String SFA_IS_INTER_ER_ENTERPRISE_GIFT = "sfa.is.inter.er.enterprise.gift";

    /**
     * 已关联高级公式
     */
    String SFA_ADD_ADVANCE_FORMULA_EXIST_WARN = "sfa.add.advance.formula.exist.warn";
    //请选择数字和金额类型的适用字段
    String SFA_ADD_ADVANCE_FORMULA_FIELD_RULE_WARN = "sfa.add.advance.formula.field.rule.warn";
    String SFA_AGGREGATE_EDIT_REBATE_POLICY_REFERENCE = "sfa.aggregate.edit.rebate.policy.reference";
    /**
     * sfa.aggregate.edit.coupon.plan.reference:该聚合规则被生效的优惠券方案[{0}]引用不可编辑
     */
    String SFA_AGGREGATE_EDIT_COUPON_PLAN_REFERENCE = "sfa.aggregate.edit.coupon.plan.reference";
    /**
     * sfa.aggregate.edit.rebate.rule.reference:该聚合规则被生效的使用返利规则[{0}]引用不可编辑
     */
    String SFA_AGGREGATE_EDIT_REBATE_RULE_REFERENCE = "sfa.aggregate.edit.rebate.rule.reference";
    /**
     * 价格政策适用客户条件最多支持{0}个
     */
    String SFA_PRICE_POLICY_ACCOUNT_CONDITION_MAXIMUM = "sfa.price.policy.account.condition.maximum";
    /**
     * 商机决策链不允许恢复
     */
    String SFA_DECISION_CHAIN_NOT_ALLOW_RECOVER = "sfa.decision.chain.not.allow.recover";
    /**
     * 没有{0}{1}的功能权限
     */
    String SFA_NOT_HAVE_FUNC_PERMISSIONS = "sfa.not.have.func.permissions";
    /**
     * 当前条件的个数，超过了最大数量15个，请调整条件个数！
     */
    String SIMPLE_PRICE_POLICY_UNIT_CONDITION_MAXIMUN = "simple.price.policy.unit.condition.maximun";

    /**
     * 无编辑权利地图的权限，请确保您拥有{0}对象的新建、编辑、列表页权限
     */
    String SFA_SAVE_TREE_MAP_NON_PRIVILEGE = "sfa.save.tree.map.non.privilege";


    /**
     * 启用报价器前，必须开启属性
     */
    String SFA_OPEN_QUOTER_CONFIG_MUST_OPEN_ATTRIBUTE = "sfa.open.quoter.config.must.open.attribute";

    /**
     * 启用报价器前，必须开启属性
     */
    String SFA_OPEN_QUOTER_CONFIG_MUST_IN_GRAY = "sfa.open.quoter.config.must.in.gray";

    /**
     * 返利使用范围
     */
    String REBATE_RULE_CONDITION_RANGE = "rebate.rule.condition.range";

    /**
     * 属性已作废或删除:{}
     */
    String QUOTER_ATTRIBUTE_DELETED = "quoter.attribute.deleted";

    /**
     * 属性值已作废或删除
     */
    String QUOTER_ATTRIBUTE_VALUE_DELETED = "quoter.attribute.value.deleted";

    /**
     * 非标属性已作废或删除
     */
    String QUOTER_NON_ATTRIBUTE_DELETED = "quoter.non.attribute.deleted";
    /**
     * sfa.spu.spec.value.not.support.modify:规格属性不支持修改
     */
    String SFA_SPU_SPEC_VALUE_NOT_SUPPORT_MODIFY = "sfa.spu.spec.value.not.support.modify";
    /**
     * 按照产品单位分组后，数量过多，请去掉部分产品
     */
    String COUPON_HARD_MODE_PRODUCT_TOTAL_TOO_MANY = "coupon.hard.mode.product.total.too.many";
    /**
     * 您没有操作此功能的权限
     */
    String SFA_UPLOAD_NOT_HAVE_PERMISSION = "sfa.upload.not.have.permission";
    /**
     * sfa.rebate.policy.rule.rebate.dimension.not.support.source:按当单值返，不支持多种返利维度
     */
    String SFA_REBATE_POLICY_RULE_REBATE_DIMENSION_NOT_SUPPORT_SOURCE = "sfa.rebate.policy.rule.rebate.dimension.not.support.source";
    /**
     * 返利产生规则的返利维度不能包含政策的返利维度
     */
    String SFA_REBATE_RULE_DIMENSION_CANNOT_INCLUDE_POLICY_DIMENSION = "sfa.rebate.rule.dimension.cannot.include.policy.dimension";
    /**
     * 返利维度中的对象，没有在返利来源对象的选项中
     */
    String SFA_REBATE_DIMENSION_NOT_AVALIABLE_IN_OPTIONS = "sfa.rebate.dimension.not.avaliable.in.options";
    /**
     * 返利维度最多只能有5个
     */
    String SFA_REBATE_RULE_DIMENSION_FIVE = "sfa.rebate.rule.dimension.five";
    /**
     * 高级单据条件，不支持【或】关联
     */
    String SFA_REBATE_POLICY_RULE_NOT_SUPPORT_OR = "sfa.rebate.policy.rule.not.support.or";
    /**
     * 高级单据条件不能为空,请录入
     */
    String SFA_REBATE_POLICY_RULE_CONDITION_PRO_NOT_EMPTY = "sfa.rebate.policy.rule.condition.pro.not.empty";
    /**
     * 高级单据条件不能超过5个条件
     */
    String SFA_REBATE_RULE_CONDITION_PRO_CANNOT_EXCEED_FIVE = "sfa.rebate.rule.condition.pro.cannot.exceed.five";
    /**
     * 最多只能设置5个"聚合规则"条件
     */
    String SFA_COUPON_PLAN_AGGREGATE_RULE_LIMIT_TOTAL_CANNOT_GT_FIVE = "sfa.coupon.plan.aggregate.rule.limit.total.cannot.gt.five";

    /**
     * 最多只能有一个"聚合规则"的条件设置为"必含"
     */
    String SFA_COUPON_PLAN_AGGREGATE_MUST_CONTAIN_RULE_LIMIT_TOTAL_CANNOT_GT_ONE = "sfa.coupon.plan.aggregate.must.contain.rule.limit.total.cannot.gt.one";
    /**
     * 高级单据公式的类型错误，请重新录入高级单据条件
     */
    String SFA_REBATE_RULE_FILTER_TYPE_ERROR = "sfa.rebate.rule.filter.type.error";

    /**
     * 一旦启用，将无法停用
     */
    String SFA_COMMON_NO_STOP = "sfa.common.no.stop";

    /**
     * 生成标准BOM方式为"自动"或"手动"时，才允许启用查重功能
     */
    String SFA_BOM_DUPLICATE_CHECK_ERROR = "sfa.bom.duplicate.check.error";
    /**
     * 所选线索池不可以为空
     */
    String SFA_PRM_LEADS_POOL_NOT_EMPTY = "sfa.prm.leads.pool.not_empty";

    /**
     * BOM查重校验失败
     */
    String SFA_BOM_DUPLICATE_SQL_CHECK_ERROR = "sfa.bom.duplicate.sql.check.error";

    /**
     * 已存在相同的标准BOM，无需重复创建！
     */
    String SFA_BOM_DUPLICATE_EXIST_ERROR = "sfa.bom.duplicate.exist.error";

    /**
     * 已存在相同的标准BOM，无需重复创建！
     */
    String SFA_BILL_EXIST_STANDARD_BOM_WARN = "sfa.bill.exist.standard.bom.warn";

    /**
     * 当前单据已关联过标准BOM，无需重复创建！
     */
    String SFA_BILL_ASSOCIATED_STANDARD_BOM_WARN = "sfa.bill.associated.standard.bom.warn";

    /**
     * 调用创建BOM接口时，无返回数据，无法关联标准BOM！
     */
    String SFA_BILL_CREATE_STANDARD_BOM_RESPONSE_NULL = "sfa.bill.create.standard.bom.response.null";

    /**
     * 来源于自动创建BOM
     */

    String SFA_BILL_STANDARD_BOM_CREATE_AUTO = "sfa.bill.standard.bom.create.auto";

    /**
     * 来源于手动创建BOM
     */
    String SFA_BILL_STANDARD_BOM_CREATE_MANUAL = "sfa.bill.standard.bom.create.manual";

    /**
     * 延期天数：{0}天；延期原因：{1}
     */
    String SFA_EXTEND_EXPIRE_TIME_CHANGE_LOG = "sfa.account.sfa_extend_expire_time_change_log";

    /**
     * 今日转线索数量已达上限。
     */
    String SFA_BIZQUERY_TRANSFER_COUNT_LIMIT = "sfa.bizquery.transfer.count.limit";

    /**
     * 年报
     */
    String SFA_BIZQUERY_REPORT_YEAR = "sfa.bizquery.report.year";

    /**
     * 全部
     */
    String SFA_LEADS_POOL_ALL = "sfa.leads.pool.all";
    /**
     * 统一社会信用代码为空！
     */
    String SFA_ACCOUNT_UNIFORM_SOCIAL_CREDIT_CODE_IS_NULL = "sfa.account.uniform_social_credit_code.is.null";
    /**
     * 合并失败，目标数据不能是源数据的子节点，请调整层级结构后重新合并
     */
    String SFA_PARTNER_MERGE_FAILED = "sfa_partner_merge_failed";
    /**
     * 未知异常
     */
    String SFA_UNKNOWN_EXCEPTION = "sfa_unknown_exception";

    /**
     * 数据不存在！
     */
    String SFA_PRODUCT_DATA_NOT_EXIST = "sfa.product.data.not.exist";
    /**
     * 数据已被删除！
     */
    String SFA_PRODUCT_DATA_DELETED = "sfa.product.data.deleted";
    /**
     * 数据没有符合的商品信息！
     */
    String SFA_PRODUCT_NO_MATCH = "sfa.product.no.match";
    /**
     * 上下架状态参数错误！
     */
    String SFA_PRODUCT_SHELF_STATUS_ERROR = "sfa.product.shelf.status.error";
    /**
     * 参数ID可能存在重复
     */
    String SFA_PRODUCT_ID_MAY_BE_DUPLICATED = "sfa.product.id.may.be.duplicated";
    /**
     * ID不存在
     */
    String SFA_PRODUCT_ID_NOT_EXIST = "sfa.product.id.not.exist";
    /**
     * 组
     */
    String SFA_PRICE_POLICY_GROUP_FLAG = "sfa.price.policy.group.flag";
    /**
     * 阶梯
     */
    String SFA_PRICE_POLICY_STAIRCASE_FLAG = "sfa.price.policy.staircase.flag";
    /**
     * 参数不能为空
     */
    String SFA_CONDITION_ARG_NOT_NULL = "sfa.condition.arg.not.null";

    /**
     * 删除失败
     */
    String SFA_CONDITION_DELETE_FAIL = "sfa.condition.delete.fail";
    /**
     * 条件组名称不得超过15个字符
     */
    String SFA_CONDITION_NOT_MORE_THAN = "sfa.condition.not.more.than";
    /**
     * 最多可保存10个条件组
     */
    String SFA_CONDITION_SUPPORT_SAVE = "sfa.condition.support.save";
    /**
     * 条件组名称已存在
     */
    String SFA_CONDITION_NAME_EXIST = "sfa.condition.name.exist";

    /**
     * 不支持更新【是否有规格】
     */
    String SFA_SPU_UPDATE_IMPORT_NOT_SUPPORT_MODIFY_SPEC = "sfa.spu.update.import.not.support.modify.spec";
    /**
     * "今日/本月 领取数量已达上限，如需修改请与管理员联系。"
     */
    String SFA_CHOOSE_TIME_LIMIT_FLAG = "sfa.choose.time.limit.flag";
    /**
     * 线索池 {0} 每月领取数量上限为 {1}，已达上限，如需修改请与管理员联系。
     */
    String SFA_CHOOSE_TIME_MONTH_LIMIT_FLAG_PLACEHOLDER = "sfa.choose.time.month.limit.flag.placeholder";

    /**
     * 线索池 {0} 每天领取数量上限为 {1}，已达上限，如需修改请与管理员联系。
     */
    String SFA_CHOOSE_TIME_DAY_LIMIT_FLAG_PLACEHOLDER = "sfa.choose.time.day.limit.flag.placeholder";
    /**
     * 时间 年度  本年度等等
     */
    String SFA_TIME_TITLE_VALUE = "sfa.time.title.value.";
    /**
     * sfa.metric.edit.field.not.allow.modify:编辑只支持修改自定义字段和
     */
    String SFA_METRIC_EDIT_FIELD_NOT_ALLOW_MODIFY = "sfa.metric.edit.field.not.allow.modify";

    // I18N.text(SFAI18NKeyUtil.)

    /**
     * 【订阅规则】千里马数据有更新
     */
    String SFA_BIDDING_SUBSCRIPTION_QLM_PUSH_TITLE = "sfa.bidding.subscription.qlm.push.title";

    /**
     * 订阅规则 {0} 推送了{1}条新数据，请查看。
     */
    String SFA_BIDDING_SUBSCRIPTION_QLM_PUSH_MSG = "sfa.bidding.subscription.qlm.push.msg";

    /**
     * 【订阅规则】获取数据成功
     */
    String SFA_BIDDING_SUBSCRIPTION_QLM_BUY_DATA_TITLE = "sfa.bidding.subscription.qlm.buy.data.title";
    /**
     * 订阅规则 {0} 获取 {1} 到 {2} 的数据成功，共消耗 {3} 条，已从资源包内扣除。
     */
    String SFA_BIDDING_SUBSCRIPTION_QLM_BUY_DATA_MSG = "sfa.bidding.subscription.qlm.buy.data.msg";

    /**
     * 订阅器未启用
     */
    String SFA_BIDDING_RULE_NOT_START = "sfa.bidding.rule.not.start";

    /**
     * 您今天查看额度已达到上限，请联系销售购买或明天再查看。
     */
    String SFA_BIDDING_NO_RESOURCE_MSG = "sfa.bidding.no.resource.msg";

    /**
     * 未找到千里马账号
     */
    String NO_QIANLIMA_ACCOUNT_FOUND = "sfa.no.qianlima.account.found";
    /**
     * 查询条件不能为空
     */
    String THE_QUERY_CRITERIA_CANNOT_BE_EMPTY = "sfa.the.query.criteria.cannot.be.empty";
    /**
     * 仅支持查询一年内数据
     */
    String ONLY_SUPPORTS_QUERYING_DATA_WITHIN_ONE_YEAR = "sfa.only.supports.querying.data.within.one.year";
    /**
     * 请输入2019年1月1日之后的日期
     */
    String SFA_BIDDING_QLM_LIST_START_TIME = "sfa.bidding.qlm.querying.start.time";
    /**
     * 已达到线索领取上限，无法恢复
     */
    String CLUE_COLLECTION_HAS_REACHED_THE_MAXIMUM_LIMIT = "sfa.clue.collection.has.reached.the.maximum.limit";
    /**
     * 订单产品
     */
    String ORDER_PRODUCT = "sfa.order.product";
    /**
     * 至 （时间 至 时间）
     */
    String TO = "sfa.to.data";
    /**
     * 更换负责人失败
     */
    String FAILED_TO_REPLACE_THE_RESPONSIBLE_PERSON = "sfa.failed.to.replace.the.responsible.person";
    /**
     * 更换负责人成功
     */
    String SUCCESSFULLY_REPLACED_THE_RESPONSIBLE_PERSON = "sfa.successfully.replaced.the.responsible.person";
    /**
     * 参数错误，请指定企业号
     */
    String PLEASE_SPECIFY_THE_ENTERPRISE_NUMBER = "sfa.please.specify.the.enterprise.number";
    /**
     * 失败
     */
    String FAIL = "sfa.fail";
    /**
     * 批量新建通用字符集
     */
    String BATCH_CREATE_UNIVERSAL_CHARACTER_SET = "sfa.batch.create.universal.character.set";
    /**
     * 给线索描述中添加字段
     */
    String ADD_FIELDS_TO_THE_CLUE_DESCRIPTION = "sfa.add.fields.to.the.clue.description";
    /**
     * 创建字段的依赖关系
     */
    String CREATE_DEPENDENCY_RELATIONSHIPS_FOR_FIELDS = "sfa.create.dependency.relationships.for.fields";
    /**
     * 查询新的预览流程接口失败
     */
    String FAILED_TO_QUERY_NEW_PREVIEW_PROCESS_INTERFACE = "sfa.failed.to.query.new.preview.process.interface";
    /**
     * 更新风险信息完成
     */
    String SFA_UPDATE_RISK_INFORMATION_COMPLETED = "sfa.update.risk.information.completed";
    /**
     * 更新风险信息失败
     */
    String SFA_FAILED_TO_UPDATE_RISK_INFORMATION = "sfa.failed.to.update.risk.information";
    /**
     * "更新风险信息"操作，已完成
     */
    String SFA_UPDATE_RISK_INFORMATION_COMPLETED_MSG = "sfa.update.risk.information.completed.msg";
    /**
     * 未查询到工商风险信息
     */
    String SFA_NO_BUSINESS_RISK_INFORMATION_FOUND = "sfa.no.business.risk.information.found";
    /**
     * 对象关联了不同的合作伙伴，不能进行合并操作。请关联相同合作伙伴或解除关联合作伙伴后再进行操作
     */
    String SFA_ACCOUNT_NOT_MERGE_LINK_PARTNER_ID = "sfa.account.not.merge.link.partnerId";

    /**
     * 纸质券号不支持修改
     */
    String SFA_COUPON_INSTANCE_PAPER_NUMBER_NOT_SUPPORT_MODIFY = "sfa.coupon.instance.paper.number.not.support.modify";
    /**
     * sfa.rebate.rule.product.range.is.empty:返利规则类型为金额返利时，产品范围不能为空
     */
    String SFA_REBATE_RULE_PRODUCT_RANGE_IS_EMPTY = "sfa.rebate.rule.product.range.is.empty";
    /**
     * sfa.rebate.rule.not.support.or:单据条件不支持【或】
     */
    String SFA_REBATE_RULE_NOT_SUPPORT_OR = "sfa.rebate.rule.not.support.or";

    String SFA_FUNCTION_BIND_MSG_SUCCESS = "sfa.function.bind.msg.success";

    String SFA_FUNCTION_BIND_MSG_FAIL = "sfa.function.bind.msg.fail";

    String SFA_FUNCTION_UNBIND_MSG_SUCCESS = "sfa.function.unbind.msg.success";

    String SFA_FUNCTION_UNBIND_MSG_FAIL = "sfa.function.unbind.msg.fail";

    /**
     * 删除
     */
    String SFA_DELETE_LABEL = "sfa.delete.label";
    /**
     * 人脉关系雷达功能未开启，请到后台管理配置中阅读相关说明，开启后使用。
     */
    String SFA_CONTACT_RELATIONSHIP_ADMIN_TITTLE = "sfa.contact.relationship.admin.tittle";
    /**
     * 人脉关系雷达功能未开启，请联系企业CRM管理员进行开启后使用。
     */
    String SFA_CONTACT_RELATIONSHIP_MEMBER_TITTLE = "sfa.contact.relationship.member.tittle";
    /**
     * 数据库不存
     */
    String SFA_LEADS_MERGE_DATA_NOT_EXIST = "sfa.leads.merge.data.not.exist";
    /**
     * 合并成功
     */
    String SFA_MERGE_SUCCESS_TITLE = "sfa.merge.success.title";
    /**
     * 暂无规则可设置
     */
    String SFA_RECYCLING_RULE_SET_DEPARTMENT_TITLE = "sfa.recycling.rule.set.department.title";
    /**
     * 将所有回收规则设置为
     */
    String SFA_SET_DEPARTMENT_CONTENT = "sfa.set.department.content";
    /**
     * sfa.rebate.policy.must.have.one:单据条件和单据高级条件必须有其中一个
     */
    String SFA_REBATE_POLICY_MUST_HAVE_ONE = "sfa.rebate.policy.must.have.one";

    /**
     * 招投标公告转客户：招标主体为空
     */
    String SFA_PROCUREMENT_INFO_THE_BODY_IS_EMPTY = "sfa.procurement.info.the.body.is.empty";
    /**
     * 客户对象的主属性字段不允许重复
     */
    String SFA_MAIN_MERGE_CHECK_NAME = "sfa.main.merge.check.name.title";


    /**
     * 合作伙伴协议：{0} 被协议签约配置使用中，请先调整协议签约配置解除使用后再进行删除操作。
     */
    String CHANNEL_PARTNER_AGREEMENT_OCCUPIED = "channel.partner.agreement.occupied";
    /**
     * 到期提醒设置非手动提醒，无法进行发起签约操作。
     */
    String CHANNEL_SIGN_SETTING_NON_MANUAL_NOTICE = "channel.sign.setting.non.manual.notice";

    /**
     * 合作伙伴规定：{0} 被浏览规定配置使用中，请先调整浏览规定配置解除使用后再进行删除操作。
     */
    String CHANNEL_PARTNER_PROVISION_OCCUPIED = "channel.partner.provision.occupied";

    /**
     * 不能进行[%s]操作，以下这些数据已被%s:[%s]。
     */
    String SFA_DATA_NOT_ANY_OPERATION = "sfa.data.not.any.operation";
    /**
     * sfa.mobile.not.support.product:移动端不支持创建和编辑产品对象，请转到网页端进行操作
     */
    String SFA_MOBILE_NOT_SUPPORT_PRODUCT = "sfa.mobile.not.support.product";

    /**
     * 字段{0}设置了高级公式，不允许被其他字段引用
     */
    String SFA_ADD_ADVANCE_FORMULA_FIELD_FORMULA_WARN = "sfa.add.advance.formula.field.formula.warn";
    /**
     * 字段{0}被{1}字段引用，不允许设置高级公式
     */
    String SFA_ADD_ADVANCE_FORMULA_FIELD_FORMULA_QUOTE_WARN = "sfa.add.advance.formula.field.formula.quote.warn";
    /**
     * 没有关联客户主数据，无法展示
     */
    String SFA_ORGANIZATIONAL_DISTRIBUTION_NOT_LINK_MAIN_DATA = "SFA_ORGANIZATIONAL_DISTRIBUTION_NOT_LINK_MAIN_DATA";


    /**
     * sfa.gift.fixed.attribute.api.not.exist:{0}对象不存在
     */
    String SFA_GIFT_FIXED_ATTRIBUTE_API_NOT_EXIST = "sfa.gift.fixed.attribute.api.not.exist";
    /**
     * sfa.gift.fixed.attribute.field.not.exist:{0}字段不存在
     */
    String SFA_GIFT_FIXED_ATTRIBUTE_FIELD_NOT_EXIST = "sfa.gift.fixed.attribute.field.not.exist";
    /**
     * sfa.gift.fixed.attribute.field.repeat:{0}对象中，字段有重复
     */
    String SFA_GIFT_FIXED_ATTRIBUTE_FIELD_REPEAT = "sfa.gift.fixed.attribute.field.repeat";
    /**
     * sfa.price.policy.gift.fixed.attribute.not.exist:配置的赠品指定属性{0}不存在
     */
    String SFA_PRICE_POLICY_GIFT_FIXED_ATTRIBUTE_NOT_EXIST = "sfa.price.policy.gift.fixed.attribute.not.exist";
    /**
     * sfa.price.policy.gift.fixed.attribute.not.in.config:赠品指定的属性{0}，在配置中不存在
     */
    String SFA_PRICE_POLICY_GIFT_FIXED_ATTRIBUTE_NOT_IN_CONFIG = "sfa.price.policy.gift.fixed.attribute.not.in.config";
    /**
     * sfa.gift.fixed.attribute.field.not.support.type:字段{0}不支持配置到指定属性
     */
    String SFA_GIFT_FIXED_ATTRIBUTE_FIELD_NOT_SUPPORT_TYPE = "sfa.gift.fixed.attribute.field.not.support.type";
    /**
     * sfa.price.policy.formula.right.empty:公式右侧不能为空
     */
    String SFA_PRICE_POLICY_FORMULA_RIGHT_EMPTY = "sfa.price.policy.formula.right.empty";
    /**
     * sfa.price.policy.formula.field.not.in.condition:高级公式中的字段，必须在规则条件，或执行量条件进行配置
     */
    String SFA_PRICE_POLICY_FORMULA_FIELD_NOT_IN_CONDITION = "sfa.price.policy.formula.field.not.in.condition";
    /**
     * sfa.price.policy.formula.aggregate.rule.not.in.condition:高级公式中的聚合规则，必须在规则条件，或执行量条件进行配置
     */
    String SFA_PRICE_POLICY_FORMULA_AGGREGATE_RULE_NOT_IN_CONDITION = "sfa.price.policy.formula.aggregate.rule.not.in.condition";
    /*
     * 销售记录关联对象不允许为空
     */
    String SFA_SALES_RECORD_RELATION_OBJECT_NOT_ALLOW_EMPTY = "sfa.sales.record.relation.object.not.allow.empty";
    /**
     * 销售记录关联对象不允许多于2个。
     */
    String SFA_SALES_RECORD_RELATION_OBJECT_NOT_ALLOW_MORE_THAN_TWO = "sfa.sales.record.relation.object.not.allow.more.than.two";

    /**
     * 线索转换时，必须转换项设置：
     */
    String SFA_LEADS_CONVERT_MUST_CONVERT_TO = "sfa.leads.convert.must.convert.to";

    /**
     * 业务类型 {0} 必须转换为：
     */
    String SFA_BUSINESS_TYPE_MUST_CONVERT_TO = "sfa.business.type.must.convert.to";

    /**
     * 此线索满足后台定义的归集规则，保存后将被自动归集，是否继续保存？
     */
    String SFA_LEADS_SAVE_AUTO_COLLECT = "sfa.leads.save.auto.collect";

    /**
     * 模板错误，系统预置成交规则不允许手动更新成交状态
     */
    String SFA_DEAL_RULE_TEMPLATE_ERROR = "sfa.deal.rule.template.error";
    /**
     * 转换者
     */
    String SFA_CONVERTER = "sfa.converter";


    /**
     * 选择的关联对象与模板不一致！
     */
    String SFA_SALES_RECORD_RELATION_OBJECT_NOT_MATCH_TEMPLATE = "sfa.sales.record.relation.object.not.match.template";

    /**
     * 您的渠道准入功能已过期，请重新购买后再继续操作。
     */
    String SFA_PARTNER_CHANNEL_MODULE_EXPIRED = "sfa.partner.channel.module.expired";
    /**
     * 数据已签约，不可进行此操作。
     */
    String SFA_PARTNER_CHANNEL_DATA_SIGNED = "sfa.partner.channel.data.signed";
    /**
     * 单位
     */
    String SFA_CRM_SPEC_UNIT = "sfa.crm.spec.unit";
    /**
     * 规格
     */
    String SFA_CRM_SPEC = "sfa.crm.spec";
    /**
     * 不允许有两个主地址。
     */
    String SFA_NOT_ALLOW_MANY_MAIN_ADDR = "sfa.not.allow.many.main.addr";
    /**
     * 不允许有两个收货地址。
     */
    String SFA_NOT_ALLOW_MANY_SHIP_ADDR = "sfa.not.allow.many.ship.add.addr";
    /**
     * sfa.coupon.instance.used:该优惠券实例已经使用，不能核销
     */
    String SFA_COUPON_INSTANCE_USED = "sfa.coupon.instance.used";
    /**
     * sfa.redeem.success:优惠券实例核销成功
     */
    String SFA_REDEEM_SUCCESS = "sfa.redeem.success";

    String SFA_COUPON_DISCOUNT_ERROR = "sfa.coupon.discount.error";

    /**
     * 会员与优惠券方案中的忠诚度计划不一致，请重新选择
     */
    String SFA_COUPON_MEMBER_PROGRAM_NOT_MATCH = "sfa.coupon.member.program.not.match";
    /**
     * 优惠券方案为自由面额券，实例上的面额不能为空值，请录入
     */
    String SFA_COUPON_INSTANCE_AMOUNT_NOT_EMPTY = "sfa.coupon.instance.amount.not.empty";
    /**
     * sfa.coupon.instance.can.not.modify.field:{0}不允许更新
     */
    String SFA_COUPON_INSTANCE_CAN_NOT_MODIFY_FIELD = "sfa.coupon.instance.can.not.modify.field";

    /**
     * 未查到当前好友数据
     */
    String SFA_CURRENT_FRIEND_NOT_FOUND = "sfa.current.friend.not.found";
    /**
     * 未查到当前群聊数据
     */
    String SFA_CURRENT_GROUP_NOT_FOUND = "sfa.current.group.not.found";

    String SFA_COMMON_TOKEN_HTTP_RESPONSE_ERROR = "sfa.common.token.http.response.error";
    String SFA_COMMON_TOKEN_PARAMETER_EMPTY = "sfa.common.token.parameter.empty";
    /**
     * 参数格式不正确
     */
    String SFA_CONTRACT_CONNECTOR_FORMAT_ERROR = "sfa.contract.connector.format.error";

    /**
     * 【预设业务类型】不能设置为主合同
     */
    String CONTRACT_DEFAULT_RECORD_TYPE_NOT_MAIN_TITLE = "contract.default.record.type.not.main.title";
    /**
     * 【附加协议】不能设置为子合同
     */
    String ADDITIONAL_CONTRACT_DEFAULT_RECORD_TYPE_NOT_MAIN_TITLE = "additional.contract.default.record.type.not.main.title";

    /**
     * 子合同业务类型不允许设置子合同分组
     */
    String CONTRACT_RECORD_TYPE_CHILDREN_NOT_MAIN_TITLE = "contract.record.type.children.not.main.title";

    /**
     * 应用key已存在，请不要重复新建
     */
    String SFA_CONTRACT_CONNECTOR_APP_KEY_EXISTS = "sfa.contract.connector.app.key.exists";

    /**
     * 周期性产品开启后不允许关闭
     */
    String SFA_CYCLICAL_PRODUCTS_NOT_ALLOW_CLOSE = "sfa.cyclical.products.not.allow.close";

    /**
     * 未与编码为[{0}]的平台做合同对接
     */
    String SFA_OUT_CONTRACT_SERVICE_NOT_EXIT = "sfa.out.contract.service.not.exit";

    /**
     * 售中后台未配置合同对接平台信息
     */
    String SFA_CONTRACT_NOT_BIND_INFO_ERROR = "sfa.contract.not.config.info.error";
    /**
     * 获取合同信息失败: {0}
     */
    String SFA_CONTRACT_GET_ERROR = "sfa.contract.get.error";
    /**
     * 调用集成平台的创建连接器接口失败: {0}
     */
    String SFA_CONTRACT_CREATE_CONNECTOR_ERROR = "sfa.contract.create.connector.error";
    /**
     * 调用甄零接口失败,请求地址:{0},错误信息:{1}
     */
    String SFA_ZHENLING_REQUEST_ERROR = "sfa.zhenling.request.error";
    /**
     * 请求合同接口参数不能为空
     */
    String SFA_CONTRACT_PARAM_ERROR = "sfa.contract.param.error";

    /**
     * 业务类型映射不能为空
     */
    String SFA_CONTRACT_PARAM_RECORD_TYPE_MAPPING_NULL = "sfa.contract.param.record.type.mapping.null";

    /**
     * 未开启合同分层结构，不允许设置合同字段映射关系，请先到后台管理进行配置
     */
    String SFA_CONTRACT_FIELD_MAPPING_NOT_ALLOW = "sfa.contract.field.mapping.not.allow";

    /**
     * 采购合同测试例子
     */
    String SFA_CONTRACT_ZHENLING_EXAMPLE_NAME = "sfa.contract.zhenling.example.name";

    /**
     * 当前对象未配置合同字段映射关系，请先到后台管理进行配置
     */
    String SFA_CONTRACT_FIELD_MAPPING_NOT_EXIST = "sfa.contract.field.mapping.not.exist";

    /**
     * 审批中 (采购合同测试例子使用)
     */
    String SFA_CONTRACT_ZHENLING_APPROVE_STATUS_APPROVING = "sfa.contract.zhenling.approve.status.approving";

    /**
     * 查询不到数据，请检查数据是否存在
     */
    String SFA_FIND_CONTRACT_PARAM_ERROR = "sfa.find.contract.not.exist.error";
    /**
     * 当前对象未绑定合同原件插件
     */
    String SFA_CONTRACT_PLUGIN_NOT_BIND_ERROR = "sfa.contract.plugin.not.bind.error";
    /**
     * 产品【{0}】的【整期售卖】属性为"是"，当前选择的时间区间非整期，请调整时间或者修改产品属性！
     */
    String SFA_CYCLE_PRODUCT_PERIOD_WARN = "sfa.cycle.product.period.warn";
    /**
     * 产品{0}服务开始时间不能大于服务结束时间
     */
    String SFA_CYCLE_PRODUCT_TIME_WARN = "sfa.cycle.product.time.warn";
    /**
     * 产品【{0}】的【{0}】有误，请核对！
     */
    String SFA_CYCLE_PRODUCT_PERIOD_VALUE_WARN = "sfa.cycle.product.period.value.warn";
    /**
     * [期数]与[服务开始时间]和[服务结束时间]不匹配
     */
    String SFA_IMPORT_CYCLE_PRODUCT_PERIOD_VALUE_WARN = "sfa.import.cycle.product.period.value.warn";

    /**
     * 周期性产品，%s不能为空
     */
    String SFA_CYCLICAL_PRODUCTS_DISPLAY_NAME_NOTNULL = "sfa.cyclical.products.display.name.notnull";
    /**
     * 一次性产品，%s不能有值
     */
    String SFA_CYCLICAL_PRODUCTS_CANNOT_HAVE_VALUE = "sfa.cyclical.products.cannot.have.value";
    /**
     * 周期性产品不能被设置为非标品
     */
    String SFA_CYCLICAL_PRODUCTS_CANNOT_NON_STANDARD = "sfa.cyclical.products.cannot.non.standard";
    /**
     * 非标产品不允许被设置为产品组合
     */
    String SFA_NON_STANDARD_PRODUCT_CANNOT_BOM = "sfa.non.standard.product.cannot.bom";
    /**
     * 一次性产品，%s等周期性产品的信息不需要，请清空！
     */
    String SFA_ONE_PRODUCTS_CANNOT_HAVE_VALUE = "sfa.one.products.cannot.have.value";
    /**
     * 周期性产品，%s等周期性产品的信息不能为空！
     */
    String SFA_CYCLE_PRODUCTS_NOTNULL = "sfa.cycle.products.notnull";
    /**
     * 短信服务异常，请稍后重试
     */
    String SFA_SMS_SERVICE_ERROR = "sfa.sms.service.error";
    /**
     * 无法匹配到短信模版明细数据，请确认模版是否已配置
     */
    String SFA_SMS_TEMPLATE_DETAIL_NOT_FOUND = "sfa.sms.template.detail.not.found";
    /**
     * 该短信模版状态为空，不可用，请更换其他模版
     */
    String SFA_SMS_TEMPLATE_STATUS_EMPTY = "sfa.sms.template.status.empty";
    /**
     * 短信模版审核状态为【{0}】，请等待审核通过后再试
     */
    String SFA_SMS_TEMPLATE_STATUS_AUDITING = "sfa.sms.template.status.auditing";
    /**
     * 未开启开票明细，不能开启开票多来源
     */
    String SFA_NOT_NEW_INVOICE_CANNOT_MULTI_SOURCE = "sfa.not.new.invoice.cannot.multi.source";
    /**
     * 关闭基于订单开票才可以开启开票多来源
     */
    String SFA_INVOICE_ORDER_BINDING_CANNOT_MULTI_SOURCE = "sfa.invoice.order.binding.cannot.multi.source";

    /**
     * 不存在的配置
     */
    String SFA_SIMPLE_CONFIG_KEY_NOT_FOUND = "sfa.simple.config.not.found";
    /**
     * 未开启开票多来源，不能开启开票明细必填开关
     */
    String SFA_NOT_MULTI_SOURCE_CANNOT_OPEN_REQUIRED = "sfa.not.multi.source.cannot.open.required";
    /**
     * 未开启开票多来源，不能进行多来源设置
     */
    String SFA_NOT_MULTI_SOURCE_CANNOT_SET = "sfa.not.multi.source.cannot.set";
    /**
     * 请填写{0}（请填写开票明细）
     */
    String SFA_PLEASE_FILL_IN = "sfa.please.fill.in";

    //产品[{0}]整期售卖,[{1}]字段不能为小数
    String SFA_PERIOD_PRODUCT_PERIOD_CHECK = "sfa.period.product.period.check";

    //产品{0},[{1}]字段不能小于等于0
    String SFA_PERIOD_PRODUCT_PERIOD_ZERO_CHECK = "sfa.period.product.period.zero.check";

    //一次性产品{0}只能为1
    String SFA_PERIOD_PRODUCT_PERIOD_ONE_CHECK = "sfa.period.product.period.one.check";

    //{0}不能为零
    String SFA_BOM_PERIOD_PRODUCT_PERIOD_ZERO_CHECK = "sfa.bom.period.product.period.zero.check";

    //一次性产品,字段{0}不能填写
    String SFA_PERIOD_PRODUCT_PERIOD_ONE_CHECK_FIELD = "sfa.period.product.period.one.check.field";


    /**
     * 已经有实例数据，自由面额券不允许编辑
     */
    String SFA_COUPON_PLAN_ALLOW_CUSTOMER_AMOUNT_NOT_MODIFY = "sfa.coupon.plan.allow.customer.amount.not.modify";
    /**
     * 周期性产品，%s不能小于0
     */
    String SFA_CYCLE_PRODUCTS_FREQUENCY_ZERO_CHECK = "sfa.cycle.products.frequency.zero.check";
    /**
     * 暂不支持未知类型聊天的模糊搜索
     */
    String SFA_UNSUPPORT_UNKNOW_SESSION_TYPE = "sfa.unsupport.unknow.session.type";
    String SFA_INTERACT_QUESTION_GET_AI_SUGGESTION = "sfa.interact.question.get_ai_suggestion";
    /**
     * 引用字段不支持
     */
    String SFA_INTERACTION_STRATEGY_FIELD_REFERENCE_NOT_SUPPORT = "sfa.interaction.strategy.field.reference.not.support";
    /**
     * 字段未启用
     */
    String SFA_INTERACTION_STRATEGY_FIELD_NOT_ACTIVE = "sfa.interaction.strategy.field.not.active";
    /**
     * sfa.interaction.strategy.limit.exceed:互动策略数量超过最大限制{0}个
     */
    String SFA_INTERACTION_STRATEGY_LIMIT_EXCEED = "sfa.interaction.strategy.limit.exceed";
    /**
     * 互动策略明细不能为空，请录入
     */
    String SFA_INTERACTION_STRATEGY_DETAIL_NOT_EMPTY = "sfa.interaction.strategy.detail.not.empty";
    /**
     * 互动策略明细顺序不能重复
     */
    String SFA_INTERACTION_STRATEGY_DETAIL_ORDER_DUPLICATE = "sfa.interaction.strategy.detail.order.duplicate";
    /**
     * 使用对象不存在
     */
    String SFA_INTERACTION_STRATEGY_USED_OBJECT_NOT_EXISTS = "sfa.interaction.strategy.used.object.not.exists";

    /**
     * activity-會議摘要
     */
    String SFA_ACTIVITY_MEETING_SUMMARY_RESULT_IS_EMPTY = "sfa.activity.meeting.summary.result.is.empty";
    String SFA_ACTIVITY_MEETING_SUMMARY_PARAM_IS_EMPTY = "sfa.activity.meeting.summary.param.is.empty";
    String SFA_FORBID_INCREMENT_UPDATE_ORDER_PRODUCT_QUANTITY = "sfa.forbid.increment.update.order_product_quantity";

    /**
     * 不允许执行关闭操作
     */
    String SFA_ADDITIONAL_CONTRACT_CLOSE_OPERATION_NOT_ALLOWED = "sfa.additional.contract.close.operation.not.allowed";

    /**
     * 开启销售合同分层结构失败
     */
    String SFA_ADDITIONAL_CONTRACT_OPEN_FAILURE_TITLE = "sfa.additional.contract.open.failure.title";
    /**
     * 不允许重复开启操作
     */
    String SFA_NOT_ALLOWED_REOPEN_OPERATION = "sfa.not.allowed.reopen.operation";

    /**
     * 添加字段失败
     */
    String SFA_ADD_FIELD_ERROR = "sfa.add.field.error";

    /**
     * 添加按钮失败
     */
    String SFA_ADD_BUTTON_ERROR = "sfa.add.button.error";

    /**
     * 添加权限失败
     */
    String SFA_ADD_PRIVILEGE_ERROR = "sfa.add.privilege.error";

    /**
     * 更新历史数据失败
     */
    String SFA_UPDATE_HISTORY_DATA_ERROR = "sfa.update.history.data.error";

    /**
     * 您未开启合作伙伴，请开启合作伙伴后再开启渠道准入。
     */
    String SFA_PARTNER_NOT_OPEN_WHEN_OPEN_CHANNEL_ACCESS = "sfa.partner.not.open.when.open.channel.access";
    /**
     * 开启、不开启{0}不能开启分层定价
     */
    String SFA_CONFIG_NOT_OPEN_STRATIFIED_PRICING = "sfa.config.not.open.stratified.price.warn";
    String SFA_CONFIG_OPEN_STRATIFIED_PRICING = "sfa.config.open.stratified.price.warn";
    /**
     * 公式有调整，请调整公式后重新开启
     */
    String SFA_CONFIG_FORMULA_ADJUST_NOT_OPEN_STRATIFIED_PRICING = "sfa.config.formula.adjust.not.open.stratified.price.warn";
    /**
     * 新建或编辑附加协议时，主合同编码和主子合同标识不能为空
     */
    String BIZVALIDATOR_SALECONTRACT_PARENTCONTRACTID_NOT_NULL = "bizvalidator.salecontract.parentContractId.not.null";

    /**
     * 新建或编辑子合同时，对应的主合同编码不能是子合同
     */
    String BIZVALIDATOR_SALECONTRACT_PARENT_NOT_MATCH = "bizvalidator.salecontract.parent.not.match";
    /**
     * 新建或编辑主合同时，主子合同标识不能为空
     */
    String BIZVALIDATOR_SALECONTRACT_CONTRACT_TYPE_NOT_NULL = "bizvalidator.salecontract.contract.type.not.null";
    /**
     * 新建或编辑主合同时，主合同编码不能有值
     */
    String BIZVALIDATOR_SALECONTRACT_PARENT_CONTRACT_ID_NEED_NULL = "bizvalidator.salecontract.parent.contract.id.need.null";
    /**
     * 业务类型与主子合同标识不匹配，请检查并修正
     */
    String BIZVALIDATOR_SALECONTRACT_RECORDTYPE_NOT_MATCH = "bizvalidator.salecontract.recordtype.not.match";

    /**
     * 如果是否分层定价不为是，则开始数量及结束数量不能有值
     */
    String SFA_NO_STRATIFIED_PRICE_START_END_COUNT_NOT_VALUE = "sfa.no.stratified.price.start.end.count.not.value";


    /**
     * 同一价目表中，同一个产品，是否分层定价必须相同
     */
    String SFA_SAME_PRICE_BOOK_PRODUCT_STRATIFIED_PRICE_SAME = "sfa.same.price.book.product.stratified.price.same";

    /**
     * 分层条数限制{0}层
     */
    String SFA_STRATIFIED_PRICE_ROW_NUMBER_LIMIT = "sfa.stratified.price.row.number.limit";

    /**
     * 分层价格必须从0开始
     */
    String SFA_STRATIFIED_PRICE_START_COUNT_ZERO = "sfa.stratified.price.start.count.zero";

    /**
     * 分层价格区间必须连续
     */
    String SFA_STRATIFIED_PRICE_COUNT_CONTINUOUS = "sfa.stratified.price.count.continuous";

    /**
     * 同一分层价格中的折扣必须一致
     */
    String SFA_STRATIFIED_PRICE_DISCOUNT_SAME = "sfa.stratified.price.discount.same";


    /**
     * [{0}]这些业务类型已经关联过数据，业务类型的主子标识不允切换
     */
    String SFA_CONTRACT_CONNECTOR_RECORD_TYPE_HAS_DATA = "sfa.contract.connector.record.type.has.data";

    /**
     * 开启分层定价不能开启此功能
     */
    String SFA_CONFIG_OPEN_STRATIFIED_PRICE_NOT_OPEN_OTHER_WARN = "sfa.config.open.stratified.price.not.open.other.warn";

    /**
     * 业务类型不存在
     */
    String SFA_SALE_CONTRACT_RECORD_TYPE_NOT_EXIST = "sfa.sale.contract.record.type.not.exist";

    /**
     * 执行失败，请选择相同业务类型的数据
     */
    String SFA_RECORD_TYPE_NOT_SAME = "sfa.record.type.not.same";

    /**
     * 产品定价模式不允许修改
     */
    String SFA_PRICING_MODE_NOT_ALLOW_MODIFY = "sfa.pricing.mode.not.allow.modify";

    /**
     * 产品类型不允许修改
     */
    String SFA_PRODUCT_TYPE_NOT_ALLOW_MODIFY = "sfa.product.type.not.allow.modify";
    /**
     * 未开启销售合同，不允许启用此功能
     */
    String SFA_OPEN_CONTRACT_MULTI_LEVEL_STRUCTURE_FAIL = "sfa.open.contract.multi.level.structure.fail";
    /**
     * SCRM继承
     */
    String SFA_SCRM_INHERIT = "sfa.scrm.inherit";
    /**
     *
     */
    String SFA_INTERACT_QUESTION_SET_MARK = "sfa.interact.question.set_mark";
    /**
     *映射关系中的字段在主数据描述中不存在
     */
    String SFA_MAPPING_FIELD_NOT_EXIST_OF_MAIN = "sfa.mapping.field.not.exist.of.main";

    /**
     * 目标不能为空
     */
    String SFA_CONTRACT_PROGRESS_RULE_GOAL_NOT_EMPTY = "sfa.contract.progress.rule.goal.not.empty";

    /**
     * 完成时间不能为空或早于当前时间
     */
    String SFA_CONTRACT_PROGRESS_RULE_GOAL_COMPLETE_TIME_INVALID = "sfa.contract.progress.rule.goal.complete.time.invalid";

    /**
     * 设置目标时销售合同不能为空
     */
    String SFA_CONTRACT_PROGRESS_RULE_GOAL_CONTRACT_NOT_EMPTY = "sfa.contract.progress.rule.goal.contract.not.empty";


    /**
     * 查看进度时，销售合同不能为空
     */
    String SFA_CONTRACT_PROGRESS_RULE_GOAL_VIEW_PROGRESS_CONTRACT_NOT_EMPTY = "sfa.contract.progress.rule.goal.view.progress.contract.not.empty";

    /**
     * 指标项类型不合法:{0}
     */
    String SFA_CONTRACT_PROGRESS_RULE_GOAL_INDEX_TYPE_OBJECT_INVALID = "sfa.contract.progress.rule.goal.index.type.object.invalid";

    /**
     * 检查点时间不能早于当前时间
     */
    String SFA_CONTRACT_PROGRESS_RULE_GOAL_CHECK_TIME_BEFORE_INVALID = "sfa.contract.progress.rule.goal.check.time.before.invalid";

    /**
     * 检查点时间不能晚于完成时间
     */
    String SFA_CONTRACT_PROGRESS_RULE_GOAL_CHECK_TIME_AFTER_INVALID = "sfa.contract.progress.rule.goal.check.time.after.invalid";

    /**
     * 重新生成中，请勿重复操作！
     */
    String SFA_REGENERATE_IN_PROGRESS = "sfa.regenerate.in.progress";
    /**
     * sfa.strategy.param.id.empty:数据ID不能为空
     */
    String SFA_STRATEGY_PARAM_ID_EMPTY = "sfa.strategy.param.id.empty";
    /**
     * sfa.interaction.strategy.param.empty:参数不能为空
     */
    String SFA_INTERACTION_STRATEGY_PARAM_EMPTY = "sfa.interaction.strategy.param.empty";
    /**
     * sfa.interaction.strategy.strategy.id.empty:策略ID不能为空
     */
    String SFA_INTERACTION_STRATEGY_STRATEGY_ID_EMPTY = "sfa.interaction.strategy.strategy.id.empty";
    /**
     * sfa.interaction.strategy.task.running:当前互动策略正在应用历史数据计算中，请勿多次点击
     */
    String SFA_INTERACTION_STRATEGY_TASK_RUNNING = "sfa.interaction.strategy.task.running";
    /**
     * sfa.interaction.strategy.task.create.error:创建任务失败
     */
    String SFA_INTERACTION_STRATEGY_TASK_CREATE_ERROR = "sfa.interaction.strategy.task.create.error";
    /**
     * sfa.interaction.strategy.apply.exist.accounts.success:任务提交成功
     */
    String SFA_INTERACTION_STRATEGY_APPLY_EXIST_ACCOUNTS_SUCCESS = "sfa.interaction.strategy.apply.exist.accounts.success";
    /**
     * sfa.strategy.empty:根据id未查询到互动策略
     */
    String SFA_STRATEGY_EMPTY = "sfa.strategy.empty";
    /**
     * sfa.strategy.count.max:应用历史最大生效数据为{0}条，当前数据量为{1}，请修改条件缩小数据范围
     */
    String SFA_STRATEGY_COUNT_MAX = "sfa.strategy.count.max";
    /**
     * sfa.strategy.count.error:查询条件总数量失败，请重试
     */
    String SFA_STRATEGY_COUNT_ERROR = "sfa.strategy.count.error";
    /**
     * sfa.strategy.task.submit.not.allow.edit:当前互动策略已提交任务，不可以进行编辑操作
     */
    String SFA_STRATEGY_TASK_SUBMIT_NOT_ALLOW_EDIT = "sfa.strategy.task.submit.not.allow.edit";
    /**
     * sfa.strategy.object.name.field.empty:互动策略未找到限制时间范围的字段
     */
    String SFA_STRATEGY_OBJECT_NAME_FIELD_EMPTY = "sfa.strategy.object.name.field.empty";
    /**
     * sfa.interaction.strategy.not.allow.edit.api.name:不允许编辑使用对象
     */
    String SFA_INTERACTION_STRATEGY_NOT_ALLOW_EDIT_API_NAME = "sfa.interaction.strategy.not.allow.edit.api.name";
    /**
     * 请输入数值型默认值
     */
    String SFA_NON_ATTR_DEFAULT_VALUE_WARN = "sfa.non.attr.default.value.warn";

    /**
     * 除工作开始/结束日期、工作时长(小时)、所属项目/任务名称字段外，开启了他字段的必填设置，因此无法保存。请修改字段必填设置或使用【工时记录】列表单独记录。
     */
    String SFA_ADDITIONAL_REQUIRED_FIELDS = "sfa.additional.required.fields";


}