package com.facishare.crm.sfa.utilities.util;

import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.autoconf.helper.ConfigHelper;

import org.apache.commons.lang3.StringUtils;

public class GrayUtil {

    private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");
    private static final FsGrayReleaseBiz followGray = FsGrayRelease.getInstance("sfa-follow");
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private static final String profile = ConfigHelper.getProcessInfo().getProfile();

    /*
    客户报备 白名单企业
     */
    private static final String CUS_FILING_CHECKER_LIST = "customer_filing_checker_list";
    private static final String SALES_ORDER_IDEMPOTENT = "sales_order_idempotent";
    private static final String ORDER_PROMOTION = "order_promotion";
    private static final String AVAILABLE_RANGE_LIMIT_IGNORE = "available_range_limit_ignore";
    private static final String SYNC_AVAILABLE_RANGE = "sync_available_range";
    private static final String PAGING_GET_DOWNSTREAM = "paging_get_downstream";
    private static final String EXPORT_SALES_ORDER_WITHOUT_DETAIL = "export_sales_order_without_detail";
    private static final String IGNORE_CATEGORY_FILTER = "ignore_category_filter";
    private static final String SUPPORT_SCENE_FOR_RELATED_LIST = "support_scene_for_related_list";
    private static final String PRICE_BOOK_PRODUCT_SUPPORT_POST_ACTION_ADD = "price_book_product_support_post_action_add";
    private static final String PRICE_BOOK_PRODUCT_SUPPORT_POST_ACTION_EDIT = "price_book_product_support_post_action_edit";
    private static final String FLOW_LAYOUT = "flow_layout";
    private static final String CHANGE_TO_NEW_OPPORTUNITY = "change_to_new_opportunity";
    private static final String BOM_PRICE_BOOK_PRODUCT = "bom_price_book_product";
    private static final String MOBILE_PRODUCT_PACKAGE = "mobile_product_package";
    private static final String ADD_HISTORY_ORDER_PRODUCT_FIELD = "history_order_product_field";
    private static final String GRAY_FOLLOW_UP = "gray_follow_up";
    private static final String NEW_OPPORTUNITY_ACCOUNT_EDIT = "new_opportunity_account_edit";
    private static final String SALES_ORDER_RECORD_TYPE_PROMOTION = "sales_order_record_type_promotion";
    private static final String ACCOUNT_MULTI_KEY_WORD_SEARCH = "account_multi_key_word_search";
    private static final String ACCOUNT_PATH_LEVEL_LIMIT = "account_path_level_limit";
    private static final String BOM_SAVE_MAX_NODE_COUNT = "max_bom_node";
    private static final String GRAY_STATISTICS_AMOUNT = "gray_statistics_amount";
    private static final String GRAY_INSERT_ACCOUNTADDR = "gray_insert_accountaddr";
    private static final String MULTI_UNIT_COUNT = "multi_unit_count";
    private static final String SERIALIZE_EMPTY_FOR_ACCOUNTPATH = "serialize_empty_for_accountpath";
    private static final String SHOW_ONLY_CURRENT_FOR_ACCOUNTPATH = "show_only_current_for_accountpath";
    private static final String GRAY_ALLOW_AFTER_ACTION_CHANGE = "gray_allow_after_action_change";
    private static final String PARTITION_SAVE_POLICY_RULE = "partition_save_policy_rule";
    private static final String GET_REAL_PRICE_PRODUCT_LIST = "get_real_price_product_list";
    private static final String REMOVE_DOWNSTREAM_DELIVERY_BUTTON = "remove_downstream_delivery_button";
    private static final String REMOVE_PRODUCT_NOT_IN_AVAILABLE_RANGE = "remove_product_not_in_available_range";
    private static final String BOM_UPDATE_NOTIFY_FUNCTION = "bom_update_notify_function";
    private static final String MULTI_UNIT_DECIMAL = "multi_unit_decimal";
    private static final String ACCOUNT_IMPORT_APPROVALFLOW = "account_import_approvalflow";
    private static final String SALE_CONTRACT_MOBILE_BUTTON = "sale_contract_mobile_button";
    private static final String IS_SIMPLE_PRICE_POLICY = "is_simple_price_policy"; //简易价格政策
    private static final String PRICE_POLICY_PRODUCT_CALCULATE = "price_policy_product_calculate";
    /**
     * 申请延期灰度
     */
    private static final String EXPIRE_TIME_EXTEND = "expire_time_extend";
    private static final String ATTRIBUTE = "attribute";
    private static final String APPROVAL_DEPARTMENT_MIGRATION = "approval_department_migration";
    private static final String SKIP_EDIT_FUNCTION = "skip_edit_function";
    private static final String REMOVE_INVOICE_APPLICATION_COMPONENT = "remove_invoice_application_component";
    private static final String FAST_UPDATE_ORDER = "fast_update_order";
    private static final String NEED_RECORD_MODIFY_LOG = "need_record_modify_log";
    public static final String REFACTOR_LATEST_SOLD_PRODUCT = "refactor_latest_sold_product";

    public static final String MEMORY_POLICY_LIMIT_FILTER = "memory_policy_limit_filter";
    private static final String CHECK_BOM = "check_bom";
    private static final String BOM_MASTER_SLAVE_MODE = "bom_master_slave_mode";
    private static final String BOM_CORE_BLACKLIST = "bom_core_blacklist";
    private static final String BOM_UNCHECKED_QUANTITY = "bom_unchecked_quantity";
    // 主数据应用 850 版本前的企业
    private static final String NON_850_VERSION_TENANTS = "non_850_version_tenants";

    public static final String SCRM_SERVICE_PROVIDER_EI = "scrm_service_provider_ei";
    public static final String ACCOUNT_MAIN_DATA_DUPLICATE_TENANTS = "main_data_duplicate_tenants";
    public static final String GRAY_UPDATE_SYNC_FIELD = "gray_update_sync_field";

    /**
     * 订单联合导入支持取价
     */
    public static final String ORDER_IMPORT_REAL_PRICE = "order_import_real_price";

    // A feature to skip holidays when calculating expire time.
    public static final String EXPIRE_TIME_SKIP_HOLIDAYS = "expire_time_skip_holidays";

    public static final String NEW_OPPORTUNITY_INCREMENT_SALES_PROCESS = "new_opportunity_increment_sales_process";

    private static final String PROJ_MANAGE_IMPORT_WORKFLOW = "proj_manage_import_workflow";

    // 线索分配 区分批量、非批量，不走xxljob
    private static final String LEADS_BATCH_ALLOCATE_MQ = "leads_batch_allocate_mq";

    // 线索分配 ddzs002 拆分
    private static final String LEADS_BATCH_ALLOCATE_MQ_VIP = "leads_batch_allocate_mq_vip";

    // match接口指定价格政策，是否重算已经匹配到此价格政策的订单产品
    private static final String RECALCULATE_CURRENT_PRICE_POLICY_PRODUCT = "recalculate_current_price_policy_product";
    
    // 灰度允许更新客户的leads_id
    private static final String UPDATE_LEADS_ID = "update_leads_id";

    // 灰度发送audit日志
    private static final String SFA_AUDIT_LOG = "sfa_audit_log";

    // 灰度校验价格政策
    private static final String CHECK_PRICE_POLICY = "check_price_policy";

    // 灰度校验价格政策是否调用match和有分摊没有价格政策
    private static final String CHECK_PRICE_POLICY_MATCH = "check_price_policy_match";

    // 灰度校验价目表价格是否变化
    private static final String CHECK_PRICE_BOOK_PRICE = "check_price_book_price";
    /**
     * 允许订单新建的时候校验计算结果
     */
    private static final String ALLOW_CHECK_CALCULATE_RESULT = "allow_check_calculate_result";
    private static final String FOLLOW_DEAL_SETTING_CRM_LOG = "follow_deal_setting_crm_log";
    private static final String REMAINING_TIME_2_EXPIRE_TIME = "remaining_time_2_expire_time";

    /**
     * 联系人编辑 edit
     */
    private static final String SFA_FOLLOW_CONTACT_EDIT = "SFA_follow_contact_edit";
    private static final String ENABLE_FORECAST_RULE_IMMEDIATELY = "enable_forecast_rule_immediately";
    //订单联合导入支持审批流
    private static final String SALES_ORDER_UNION_IMPORT_SUPPORT_APPROVAL_FLOW = "salesOrder_union_import_support_approvalFlow";

    // 移动端隐藏商机联系人图谱组件
    private static final String NEW_OPPORTUNITY_CONTACT_ATLAS_COMPONENT = "new_opportunity_contact_atlas_component";
    // 商机商机阶段可编辑
    private static final String EDIT_NEW_OPPORTUNITY_SALES_STAGE = "edit_new_opportunity_sales_stage";
    //灰度价目表改造
    private static final String PRICE_BOOK_REFORM = "price_book_reform";
    private static final String PRICE_BOOK_PRODUCT_CHECK_DATA_PRIVILEGE = "price_book_product_check_data_privilege";

    // 灰度属性约束
    private static final String BOM_CONSTRAINT = "bom_constraint";

    // 灰度match价格政策是否过滤已经超限额的政策
    private static final String NEED_FILTER_PRICE_POLICY_OVER_OCCUPY = "need_filter_price_policy_over_occupy";

    private static final String SKIP_CHECK_PRODUCT = "skip_check_product";
    private static final String BOM_FILTER_CURRENCY = "bom_filter_currency";
    private static final String FINAL_FILL_REF_OBJECT = "final_fill_ref_object";
    private static final String OPTIMIZE_SALES_ORDER = "optimize_sales_order";

    private static final String AUTO_GENERATE_SPU = "auto_generate_spu";

    private static final String OPTIMIZE_PRODUCT_LIST = "optimize_product_list";

    private static final String ALLOW_PRICE_BOOK_PRIORITY = "allow_price_book_priority";

    private static final String OPTIMIZE_RECORD = "optimize_record";
    private static final String OPTIMIZE_AVAILABLE_PRODUCT = "optimize_available_product";
    private static final String CHECK_STOCK_CONFIG = "check_stock_config";
    private static final String IGNORE_AVAILABLE_RANGE_CATEGORY = "ignore_available_range_category";

    private static final String GRAY_PRICE_BOOK_VALIDATE = "gray_price_book_validate";

    private static final String MULTI_PRICE_BOOK_VALIDE_FROM_ERP = "multi_price_book_valide_from_erp";

    private static final String PLAN_PAYMENT_AMOUNT_LESS_ZERO = "plan_payment_amount_less_zero";

    private static final String OPPORTUNITY_SALE_ACTION = "opportunity_sale_action";
    // 原值只使用旧值
    private static final String PRICE_POLICY_OLD_VALUE_ONLY_USE_CUSTOMER_FIELD = "price_policy_old_value_only_use_customer_field";

    private static final String CALCULATE_IGNORE_CLAIMED_TIME = "calculate_ignore_claimed_time";

    private static final String GRAY_LEADS_TRANSFER_MERGE_FUNCTION_RESULT = "gray_leads_transfer_merge_function_result";

    private static final String FILTER_BOM_ACCORDING_PRICE_BOOK = "filter_bom_according_priceBook";
    /**
     * 灰度控制,特别多的规格值查询走拆分查询
     */
    private static final String SEARCH_SPEC_AND_VALUES_BY_SPU_SPLIT_WHERE = "search_spec_and_values_by_spu_split_where";

    /**
     * 灰度控制，查询规格，规格值不超过2000
     */
    private static final String SEARCH_SPEC_AND_VALUES_BY_SPU_LIMIT = "search_spec_and_values_by_spu_limit";

    private static final String SHOW_WORKING_HOURS_SUM_FIELD = "show_working_hours_sum_field";

    public static final String NEW_OPPORTUNITY_DO_AFTER_ASYNC = "new_opportunity_do_after_sync";

    // 价格政策黑名单设置行index+政策
    public static final String MATCH_BLACK_LIST_INDEX_AND_POLICY = "match_black_list_index_and_policy";

    public static final String GRAY_CALC_PRICE_BY_UNIT = "gray_calc_price_by_unit";

    public static final String GRAY_LEADS_TRANSFER_CONFIG_NEW = "gray_leads_transfer_config_new";

    public static final String GRAY_TRANSFER_FUNC_CODE_NEW = "gray_transfer_func_code_new";

    /**
     * 线索新建页面线索池选项 根据权限过滤
     */
    public static final String GRAY_LEADS_ADD_FILTER_POOL_OPTION_FUNC = "gray_leads_add_filter_pool_option_func";

    public static final String SPU_RELATED_LIST_OPTIMIZE = "spu_related_list_optimize";

    public static final String UNCHECK_ATTRIBUTE_VALUE_DEFAULT = "uncheck_attribute_value_default";
    public static final String NEW_INVOICE_OPEN_OPTIMIZE = "new_invoice_open_optimize";
    public static final String TEMP_BOM_ATTR_PRICE = "temp_bom_attr_price";

    //报价器灰度租户
    public static final String QUOTER_OPEN_TENANT_ID = "quoter_open_tenant_id";


    public static final String NEW_CATEGORY_SEARCH = "new_category_search";
    private static final String ACCOUNTS_RECEIVABLE_OLD_TENANTS = "accounts_receivable_old_tenants";

    private static final String ACCOUNTS_RECEIVABLE_RED_PAYMENT = "accounts_receivable_red_payment";
    private static final String RED_PAYMENT_MAX_PAYMENT_AMOUNT_VALIDATE = "red_payment_max_payment_amount_validate";
    private static final String PAYMENT_IMPORT_UNCHECK = "payment_import_uncheck";
    private static final String FORECAST_TASK_FILTER_ID_GRAY = "forecast_task_filter_id_gray";
    private static final String PAYMENT_ALLOW_AMOUNT_LT_ZERO = "payment_allow_amount_LT_zero";
    private static final String BOM_PRICE_ASYN = "bom_price_asyc";
    // 调整订单时全部重算促销不保留原促销,需求来源齐鲁动保
    private static final String POLICY_MATCH_ALL_INDEX = "policy_match_all_index";
    // 灰度控制，是否灰度了AI能力
    private static final String GRAY_FS_SHARE_AI_EI = "gray_fs_share_ai_ei";
    // 线索编辑跳过保有量校验
    private static final String LEAD_EDIT_SKIP_INVENTORY = "leads_edit_skip_inventory";

    // 灰度控制，返利单独立扣减走tcc事务控制
    private static final String GRAY_REBATE_REDUCE_TCC = "gray_rebate_reduce_tcc";
    // 灰度控制，回滚到上一版本逻辑
    private static final String ROLLBACK_CATEGORY_RETURN = "rollback_category_return";
    //灰度多单位计算价目表价格
    private static final String MULTI_CALC_PRICE_PRICE_BOOK_PRICE = "multi_calc_price_price_book_price";
    private static final String SEARCH_BY_CATEGORY_PATH = "search_by_category_path";

    private static final String BIG_BOM_TENANT_ID = "big_bom_tenant_id";
    /**
     * 针对bom企业超过200个节点的，走400的校验
     *
     * @param tenantId
     * @return
     */
    public static Boolean grayMaxBomNode(String tenantId) {
        return gray.isAllow(BOM_SAVE_MAX_NODE_COUNT, tenantId);
    }


    public static Boolean isGrayEnable(String tenantId, String grayKey) {
        return gray.isAllow(grayKey, tenantId);
    }

    /**
     * 灰度控制，企业在添加历史订单产品时
     * 订单产品上： 价目表明细id 和 产品id 不走字段权限
     *
     * @param tenantId
     * @return
     */
    public static Boolean isGrayAddHistoryOrderProductField(String tenantId) {
        return gray.isAllow(ADD_HISTORY_ORDER_PRODUCT_FIELD, tenantId);
    }

    /**
     * 控制终端是否下发产品包的产品
     *
     * @return
     */
    public static Boolean isGrayMobileProductPackage(String tenantId) {
        return gray.isAllow(MOBILE_PRODUCT_PACKAGE, tenantId);
    }

    /**
     * 该灰度目前只针对佳能
     * 去除老的灰度控制，走gray
     *
     * @param tenantId
     * @return
     */
    public static Boolean isGrayBomPriceBookProduct(String tenantId) {
        return gray.isAllow(BOM_PRICE_BOOK_PRODUCT, tenantId);
    }

    public static Boolean isGrayOrderPromotion(String tenantId) {
        //return true;
        return gray.isAllow(ORDER_PROMOTION, tenantId);
    }

    public static Boolean isIgnoreAvailableRangeLimit(String tenantId) {
        return gray.isAllow(AVAILABLE_RANGE_LIMIT_IGNORE, tenantId);
    }

    public static Boolean needPartitionSavePolicyRule(String tenantId) {
        return gray.isAllow(PARTITION_SAVE_POLICY_RULE, tenantId);
    }

    public static Boolean needRemoveDownstreamDeliveryButton(String tenantId) {
        return gray.isAllow(REMOVE_DOWNSTREAM_DELIVERY_BUTTON, tenantId);
    }

    public static Boolean needRemoveProductNotInAvailableRange(String tenantId) {
        return gray.isAllow(REMOVE_PRODUCT_NOT_IN_AVAILABLE_RANGE, tenantId);
    }

    public static boolean skipCheckProduct(String tenantId) {
        return gray.isAllow(SKIP_CHECK_PRODUCT, tenantId);
    }

    public static boolean needFilterCurrency(String tenantId) {
        return gray.isAllow(BOM_FILTER_CURRENCY, tenantId);
    }

    public static boolean finalFillRefObject(String tenantId) {
        return gray.isAllow(FINAL_FILL_REF_OBJECT, tenantId);
    }

    public static boolean needOptimizeOrder(String tenantId) {
        return gray.isAllow(OPTIMIZE_SALES_ORDER, tenantId);
    }

    public static boolean autoGenerateSpu(String tenantId) {
        return gray.isAllow(AUTO_GENERATE_SPU, tenantId);
    }

    public static boolean optimizeProductList(String tenantId) {
        return gray.isAllow(OPTIMIZE_PRODUCT_LIST, tenantId);
    }

    public static boolean forbidPriceBookPriority(String tenantId) {
        return !gray.isAllow(ALLOW_PRICE_BOOK_PRIORITY, tenantId);
    }

    public static boolean isOptimizeRecord(String tenantId) {
        return gray.isAllow(OPTIMIZE_RECORD, tenantId);
    }

    public static boolean isOptimizeAvailableProduct(String tenantId) {
        return gray.isAllow(OPTIMIZE_AVAILABLE_PRODUCT, tenantId);
    }

    public static boolean needCheckStockConfig(String tenantId) {
        return gray.isAllow(CHECK_STOCK_CONFIG, tenantId);
    }

    public static boolean ignoreAvailableRangeCategory(String tenantId) {
        return gray.isAllow(IGNORE_AVAILABLE_RANGE_CATEGORY, tenantId);
    }

    public static boolean needSync2AvailableRange(String tenantId) {
        return gray.isAllow(SYNC_AVAILABLE_RANGE, tenantId);
    }

    public static boolean exportSalesOrderWithoutDetail(String tenantId) {
        return gray.isAllow(EXPORT_SALES_ORDER_WITHOUT_DETAIL, tenantId);
    }

    public static boolean pagingGetDownstream(String tenantId) {
        return gray.isAllow(PAGING_GET_DOWNSTREAM, tenantId);
    }

    public static boolean supportFilterCategory(String tenantId) {
        return !gray.isAllow(IGNORE_CATEGORY_FILTER, tenantId);
    }

    public static boolean supportScene4RelatedList(String tenantId) {
        return gray.isAllow(SUPPORT_SCENE_FOR_RELATED_LIST, tenantId);
    }

    public static boolean supportAddPostAction(String tenantId) {
        return gray.isAllow(PRICE_BOOK_PRODUCT_SUPPORT_POST_ACTION_ADD, tenantId);
    }

    public static boolean supportEditPostAction(String tenantId) {
        return gray.isAllow(PRICE_BOOK_PRODUCT_SUPPORT_POST_ACTION_EDIT, tenantId);
    }

    public static boolean supportFlowLayout(String tenantId) {
        return gray.isAllow(FLOW_LAYOUT, tenantId);
    }

    public static Boolean isChangeToNewOpportunity(String tenantId) {
        if (Integer.parseInt(tenantId) >= 683314) {
            return true;
        } else {
            return gray.isAllow(CHANGE_TO_NEW_OPPORTUNITY, tenantId);
        }
    }

    public static Boolean isGrayFollowUp(String tenantId) {
        return gray.isAllow(GRAY_FOLLOW_UP, tenantId);
    }

    public static Boolean isSerializeEmptyForAccountPath(String tenantId) {
        return gray.isAllow(SERIALIZE_EMPTY_FOR_ACCOUNTPATH, tenantId);
    }

    public static Boolean isShowOnlyCurrentForAccountPath(String tenantId) {
        return gray.isAllow(SHOW_ONLY_CURRENT_FOR_ACCOUNTPATH, tenantId);
    }

    public static boolean IsGrayAllowAfterActionChange(String tenantId) {
        return gray.isAllow(GRAY_ALLOW_AFTER_ACTION_CHANGE, tenantId);
    }

    public static Boolean isExpireTimeExtend(String tenantId) {
        return gray.isAllow(EXPIRE_TIME_EXTEND, tenantId);
    }

    public static Boolean isGrayAttribute(String tenantId) {
        return gray.isAllow(ATTRIBUTE, tenantId);
    }

    public static Boolean isNewOpportunityAccountEdit(String tenantId) {
        return gray.isAllow(NEW_OPPORTUNITY_ACCOUNT_EDIT, tenantId);
    }

    public static Boolean isSalesOrderRecordTypePromotion(String tenantId) {
        return gray.isAllow(SALES_ORDER_RECORD_TYPE_PROMOTION, tenantId);
    }

    public static Boolean isAccountMultiKeyWordSearchUtil(String tenantId) {
        return gray.isAllow(ACCOUNT_MULTI_KEY_WORD_SEARCH, tenantId);
    }

    public static Boolean isGrayAccountPathLevelLimit(String tenantId) {
        return gray.isAllow(ACCOUNT_PATH_LEVEL_LIMIT, tenantId);
    }

    /**
     * 金额摘要接口是否并行获取开关
     *
     * @param tenantId
     * @return
     */
    public static Boolean isGrayStatisticsAmount(String tenantId) {
        return gray.isAllow(GRAY_STATISTICS_AMOUNT, tenantId);
    }

    /**
     * 新建客户同步新增地址灰度开关
     *
     * @param tenantId
     * @return
     */
    public static Boolean isGrayInsertAccountAddr(String tenantId) {
        return gray.isAllow(GRAY_INSERT_ACCOUNTADDR, tenantId);
    }

    public static Boolean isGrayMultiUnitCount(String tenantId) {
        return gray.isAllow(MULTI_UNIT_COUNT, tenantId);
    }

    public static Boolean isGetRealPriceProductList(String tenantId) {
        return gray.isAllow(GET_REAL_PRICE_PRODUCT_LIST, tenantId);
    }

    public static Boolean bomUpdateNotifyFunction(String tenantId) {
        return gray.isAllow(BOM_UPDATE_NOTIFY_FUNCTION, tenantId);
    }

    public static Boolean isMultiUnitDecimal(String tenantId) {
        return gray.isAllow(MULTI_UNIT_DECIMAL, tenantId);
    }

    /**
     * 客户导入是否允许触发审批流
     *
     * @param tenantId
     * @return
     */
    public static Boolean isAccountImportApprovalflow(String tenantId) {
        return gray.isAllow(ACCOUNT_IMPORT_APPROVALFLOW, tenantId);
    }

    public static Boolean isSaleContractMobileButton(String tenantId) {
        return gray.isAllow(SALE_CONTRACT_MOBILE_BUTTON, tenantId);
    }

    public static boolean isApprovalDepartmentMigration(String tenantId) {
        return gray.isAllow(APPROVAL_DEPARTMENT_MIGRATION, tenantId);
    }

    /**
     * 获取简易价格政策
     *
     * @param tenantId
     * @return
     */
    public static boolean isSimplePricePolicy(String tenantId) {
        return gray.isAllow(IS_SIMPLE_PRICE_POLICY, tenantId);
    }

    public static boolean skipEditFunction(String tenantId) {
        return gray.isAllow(SKIP_EDIT_FUNCTION, tenantId);
    }

    /**
     * 移除详情页开票申请组件
     */
    public static boolean needRemoveInvoiceApplicationComponent(String tenantId) {
        return gray.isAllow(REMOVE_INVOICE_APPLICATION_COMPONENT, tenantId);
    }


    public static boolean refactorLatestSold(String tenantId) {
        return gray.isAllow(REFACTOR_LATEST_SOLD_PRODUCT, tenantId);
    }

    public static boolean fastUpdateOrder(String tenantId) {
        return gray.isAllow(FAST_UPDATE_ORDER, tenantId);
    }

    public static boolean needRecordModifyLog(String tenantId) {
        return gray.isAllow(NEED_RECORD_MODIFY_LOG, tenantId);
    }

    public static boolean isGrayOrderImportRealPrice(String tenantId) {
        return gray.isAllow(ORDER_IMPORT_REAL_PRICE, tenantId);
    }

    public static boolean pricePolicyProductCalculate(String tenantId) {
        return gray.isAllow(PRICE_POLICY_PRODUCT_CALCULATE, tenantId);
    }


    /**
     * This function checks if the tenant is allowed to skip holidays when calculating the expire time
     *
     * @param tenantId The tenant ID of the tenant that is making the call.
     * @return A boolean value.
     */
    public static boolean isGrayExpireTimeSkipHolidays(String tenantId) {
        return followGray.isAllow(EXPIRE_TIME_SKIP_HOLIDAYS, tenantId);
    }

    public static boolean isNewOpportunityIncrementSalesProcess(String tenantId) {
        return followGray.isAllow(NEW_OPPORTUNITY_INCREMENT_SALES_PROCESS, tenantId);
    }


    public static boolean isGrayMemoryPolicyLimitFilter(String tenantId) {
        return gray.isAllow(MEMORY_POLICY_LIMIT_FILTER, tenantId);
    }

    /**
     * 项目管理相关的对象 导入是否允许触发工作流
     */
    public static Boolean isProjManageImportWorkflow(String tenantId) {
        return gray.isAllow(PROJ_MANAGE_IMPORT_WORKFLOW, tenantId);
    }

    /**
     * match指定价格政策，是否重算已经匹配到此价格政策的订单产品
     */
    public static boolean isRecalculateCurrentPricePolicyProduct(String tenantId) {
        return gray.isAllow(RECALCULATE_CURRENT_PRICE_POLICY_PRODUCT, tenantId);
    }

    public static boolean isLeadsBatchAllocateMQ(String tenantId) {
        return followGray.isAllow(LEADS_BATCH_ALLOCATE_MQ, tenantId);
    }

    public static boolean isLeadsBatchAllocateMQVIP(String tenantId) {
        return followGray.isAllow(LEADS_BATCH_ALLOCATE_MQ_VIP, tenantId);
    }

    /**
     * 企业是否灰度 订单新建幂等
     *
     * @param tenantId 企业id
     * @return
     */
    public static boolean isAllowSalesOrderIdempotent(String tenantId) {
        return gray.isAllow(SALES_ORDER_IDEMPOTENT, tenantId);
    }

    /**
     * 企业是否有客户报备数据 有的话就查询数据库 or 缓存
     *
     * @param tenantId
     * @return
     */
    public static boolean isCustomerFilingChecker(String tenantId) {
        return gray.isAllow(CUS_FILING_CHECKER_LIST, tenantId);
    }

    /**
     * 企业是否灰度发送audit日志
     *
     * @param tenantId
     * @return
     */
    public static boolean isSfaAuditLog(String tenantId) {
        return gray.isAllow(SFA_AUDIT_LOG, tenantId);
    }

    /**
     * 企业是否灰度校验价格政策
     *
     * @param tenantId
     * @return
     */
    public static boolean isCheckPricePolicy(String tenantId) {
        return gray.isAllow(CHECK_PRICE_POLICY, tenantId);
    }

    /**
     * 企业是否灰度校验价格政策是否调用match或者有缓存有分摊没有价格政策。
     *
     * @param tenantId
     * @return
     */
    public static boolean isCheckPricePolicyMatch(String tenantId) {
        return gray.isAllow(CHECK_PRICE_POLICY_MATCH, tenantId);
    }

    /**
     * 企业是否灰度校验价目表价格是否变化
     *
     * @param tenantId
     * @return
     */
    public static boolean isCheckPriceBookPrice(String tenantId) {
        return gray.isAllow(CHECK_PRICE_BOOK_PRICE, tenantId);
    }

    public static boolean isContactEditSendMQ(String tenantId) {
        return followGray.isAllow(SFA_FOLLOW_CONTACT_EDIT, tenantId);
    }

    public static boolean enableForecastRuleImmediately(String tenantId) {
        return gray.isAllow(ENABLE_FORECAST_RULE_IMMEDIATELY, tenantId);
    }

    public static boolean salesOrderSupportApproval(String tenantId) {
        return gray.isAllow(SALES_ORDER_UNION_IMPORT_SUPPORT_APPROVAL_FLOW, tenantId);
    }

    public static boolean isAllowNewOpportunityContactAtlasComponent(String tenantId) {
        return gray.isAllow(NEW_OPPORTUNITY_CONTACT_ATLAS_COMPONENT, tenantId);
    }

    public static boolean isPriceBookReform(String tenantId) {
        return gray.isAllow(PRICE_BOOK_REFORM, tenantId) || bizConfigThreadLocalCacheService.isOpenPriceBookReform(tenantId);
    }

    public static boolean isBomConstraint(String tenantId) {
        return gray.isAllow(BOM_CONSTRAINT, tenantId);
    }

    public static boolean needFilterPricePolicyOverOccupy(String tenantId) {
        return gray.isAllow(NEED_FILTER_PRICE_POLICY_OVER_OCCUPY, tenantId);
    }

    public static boolean isPriceBookProductCheckDataPrivilege(String tenantId) {
        return gray.isAllow(PRICE_BOOK_PRODUCT_CHECK_DATA_PRIVILEGE, tenantId);
    }

    public static boolean isGrayPriceBookValidate(String tenantId) {
        return gray.isAllow(GRAY_PRICE_BOOK_VALIDATE, tenantId);
    }

    public static boolean isMultiPriceBookValideFromErp(String tenantId) {
        return gray.isAllow(MULTI_PRICE_BOOK_VALIDE_FROM_ERP, tenantId);
    }

    public static boolean isPlanPaymentAmountLessZero(String tenantId) {
        return gray.isAllow(PLAN_PAYMENT_AMOUNT_LESS_ZERO, tenantId);
    }

    public static boolean isOpportunitySaleAction(String tenantId) {
        return gray.isAllow(OPPORTUNITY_SALE_ACTION, tenantId);
    }

    public static boolean isUseCustomerField(String tenantId) {
        return gray.isAllow(PRICE_POLICY_OLD_VALUE_ONLY_USE_CUSTOMER_FIELD, tenantId);
    }

    public static boolean isGrayCheckBom(String tenantId) {
        return gray.isAllow(CHECK_BOM, tenantId);
    }

    public static boolean isIgnoreClaimedTime(String tenantId) {
        return followGray.isAllow(CALCULATE_IGNORE_CLAIMED_TIME, tenantId);
    }

    public static boolean isGrayLeadsTransferMergeFunctionResult(String tenantId) {
        return gray.isAllow(GRAY_LEADS_TRANSFER_MERGE_FUNCTION_RESULT, tenantId);
    }

    public static boolean allowToEditNewOpportunitySalesStage(String tenantId) {
        return gray.isAllow(EDIT_NEW_OPPORTUNITY_SALES_STAGE, tenantId);
    }

    public static boolean filterBomAccordingPriceBook(String tenantId) {
        return gray.isAllow(FILTER_BOM_ACCORDING_PRICE_BOOK, tenantId);
    }

    public static boolean splitWhere(String tenantId) {
        return gray.isAllow(SEARCH_SPEC_AND_VALUES_BY_SPU_SPLIT_WHERE, tenantId);
    }

    public static boolean limitSpec(String tenantId) {
        return gray.isAllow(SEARCH_SPEC_AND_VALUES_BY_SPU_LIMIT, tenantId);
    }

    public static boolean showWorkingHoursSumField(String tenantId) {
        return gray.isAllow(SHOW_WORKING_HOURS_SUM_FIELD, tenantId);
    }

    public static boolean bomMasterSlaveMode(String tenantId) {
        return !gray.isAllow(BOM_CORE_BLACKLIST, tenantId);
    }

    public static boolean grayCalcPriceByUnit(String tenantId) {
        return gray.isAllow(GRAY_CALC_PRICE_BY_UNIT, tenantId);
    }


    /**
     * 价格政策黑名单设置行index+政策
     *
     * @param tenantId 组合ei
     * @return
     */
    public static boolean matchBlackListIndexAndPolicy(String tenantId) {
        return gray.isAllow(MATCH_BLACK_LIST_INDEX_AND_POLICY, tenantId);
    }


    public static boolean isSpuRelatedListOptimize(String tenantId) {
        return gray.isAllow(SPU_RELATED_LIST_OPTIMIZE, tenantId);
    }

    public static boolean isUncheckAttributeValueDefault(String tenantId) {
        return gray.isAllow(UNCHECK_ATTRIBUTE_VALUE_DEFAULT, tenantId);
    }

    public static boolean isNewInvoiceOpenOptimize(String tenantId) {
        return gray.isAllow(NEW_INVOICE_OPEN_OPTIMIZE, tenantId);
    }

    public static boolean BomAttrPrice(String tenantId) {
        return gray.isAllow(TEMP_BOM_ATTR_PRICE, tenantId);
    }

    public static boolean isGraySms(String tenantId) {
        return gray.isAllow("sms_open_gray", tenantId);
    }

    public static boolean isBomSpecialQuantity(String tenantId) {
        return gray.isAllow(BOM_UNCHECKED_QUANTITY, tenantId);
    }

    public static boolean isServiceProviderEi(String tenantId) {
        return gray.isAllow(SCRM_SERVICE_PROVIDER_EI, tenantId);
    }

    public static boolean is850VersionBeforeTenant(String tenantId) {
        return gray.isAllow(NON_850_VERSION_TENANTS, tenantId);
    }

    public static boolean isGrayAccountMainDataDuplicate(String tenantId) {
        return gray.isAllow(ACCOUNT_MAIN_DATA_DUPLICATE_TENANTS, tenantId);
    }

    public static boolean isGrayUpdateSyncField(String downstreamTenantId) {
        return gray.isAllow(GRAY_UPDATE_SYNC_FIELD, downstreamTenantId);
    }

    public static boolean isGrayNewLeadsTransferConfig(String tenantId) {
        return gray.isAllow(GRAY_LEADS_TRANSFER_CONFIG_NEW, tenantId);
    }

    public static boolean isGrayTransferFuncCodeNew(String tenantId) {
        return gray.isAllow(GRAY_TRANSFER_FUNC_CODE_NEW, tenantId);
    }

	public static boolean isGrayNewProjectResource(String tenantId) {
		return gray.isAllow("new_project_resource", tenantId);
	}

	public static boolean isGrayAllowUpdateProjectId(String tenantId) {
		return gray.isAllow("allow_update_project_id", tenantId);
	}


    public static boolean isGrayLeadsAddFilterPoolOptionFunc(String tenantId) {
        return gray.isAllow(GRAY_LEADS_ADD_FILTER_POOL_OPTION_FUNC, tenantId);
    }

    /**
     * 客户财务对象 导入是否允许触发工作流
     */
    public static Boolean isAccountFinInfoImportWorkflow(String tenantId) {
        return gray.isAllow("account_fin_info_import_workflow", tenantId);
    }

    /**
     * 应收历史企业灰度名单
     */
    public static boolean isAccountsReceivableOldTenants(String tenantId) {
        return gray.isAllow(ACCOUNTS_RECEIVABLE_OLD_TENANTS, tenantId);
    }

    /**
     * 蒙牛二期，支持红字回款
     */
    public static boolean isAccountsReceivableRedPayment(String tenantId) {
        return gray.isAllow(ACCOUNTS_RECEIVABLE_RED_PAYMENT, tenantId);
    }

    public static boolean redPaymentMaxPaymentAmountValidate(String tenantId) {
        return gray.isAllow(RED_PAYMENT_MAX_PAYMENT_AMOUNT_VALIDATE, tenantId);
    }

    public static boolean isPaymentImportUncheck(String tenantId) {
        return gray.isAllow(PAYMENT_IMPORT_UNCHECK, tenantId);
    }

    /**
     * 采用新分类搜索
     */
    public static boolean isNewCategorySearch(String tenantId) {
        return gray.isAllow(NEW_CATEGORY_SEARCH, tenantId);
    }
    public static boolean recordCreatedByFieldLogTenant(String tenantId) {
        return gray.isAllow("record_created_by_field_log", tenantId);
    }

    /**
     * bom db灰度
     */
    public static Boolean isBOMSearchDB(String tenantId) {
        return gray.isAllow("bom_search_db", tenantId);
    }

    public static boolean grayForecastTaskFilterId(String tenantId) {
        return gray.isAllow(FORECAST_TASK_FILTER_ID_GRAY, tenantId);
    }

    public static boolean grayPaymentAllowAmountLTZero(String tenantId) {
        return gray.isAllow(PAYMENT_ALLOW_AMOUNT_LT_ZERO, tenantId);
    }

    public static boolean grayAsynBomPrice(String tenantId) {
        return gray.isAllow(BOM_PRICE_ASYN, tenantId);
    }
    public static boolean grayPolicyMatchAllIndex(String tenantId) {
        return gray.isAllow(POLICY_MATCH_ALL_INDEX, tenantId);
    }
    public static boolean grayFsShareAiEi(String tenantId) {
        return gray.isAllow(GRAY_FS_SHARE_AI_EI, tenantId);
    }

    /** 线索编辑跳过保有量校验 */
    public static boolean isLeadsEditSkipInventory(String tenantId){
        return gray.isAllow(LEAD_EDIT_SKIP_INVENTORY, tenantId);
    }


    public static boolean grayRebateReduceTcc(String tenantId) {
        return gray.isAllow(GRAY_REBATE_REDUCE_TCC, tenantId);
    }

    public static boolean upgradeNewOpportunityContactAtlas(String tenantId) {
        return gray.isAllow("upgrade_new_opportunity_contact_atlas", tenantId);
    }
    public static boolean skipCategoryLeafNodeCheck(String tenantId) {
        return gray.isAllow("skip_category_leaf_node_check", tenantId);
    }
    public static boolean isStopSyncCategoryTenant(String tenantId) {
        return gray.isAllow("stop_sync_category_tenant", tenantId);
    }
    public static boolean rollbackCategoryReturn(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return true;
        }
        return gray.isAllow(ROLLBACK_CATEGORY_RETURN, tenantId);
    }

    /**
     *
     * 待办里面屏蔽代理通线索
     * @return
     */
    public static boolean isGrayLeadsTodoListFilter(String tenantId) {
        return gray.isAllow("leads_todo_list_filter", tenantId);
    }

    /**
     * 同时获取用户 PRM 和 DHT 下游数据
     */
    public static boolean isGetUserPrmAndDhtDownstream(String tenantId) {
        return gray.isAllow("get_user_prm_and_dht_downstream", tenantId);
    }

    public static boolean isTieredPriceListOptimize(String tenantId) {
        return gray.isAllow("tiered_price_list_optimize", tenantId);
    }

    public static boolean isMultiCalcPricePriceBookPrice(String tenantId) {
        return gray.isAllow(MULTI_CALC_PRICE_PRICE_BOOK_PRICE, tenantId);
    }
    public static boolean isSearchByCategoryPathTenant(String tenantId) {
        return gray.isAllow(SEARCH_BY_CATEGORY_PATH, tenantId);
    }

    public static boolean isBigBomTenant(String tenantId) {
        return gray.isAllow(BIG_BOM_TENANT_ID, tenantId);
    }
    public static boolean leadsPoolLimitCountHigher(String tenantId) {
        return gray.isAllow("leads_pool_limit_count_higher", tenantId);
    }

    /**
     * 根据 profile 控制是否加代理, 在配置里面走代理。
     */
    public static boolean proxyByProfile() {
        if (StringUtils.isBlank(profile)){
            return false;
        }
        return gray.isAllow("proxy_by_profile", profile);
    }

    public static boolean isActivityRecordingEnableOss(String tenantId) {
        return gray.isAllow("activity_recording_enable_oss", tenantId);
    }

    public static boolean allowActiveRecordObjectification(String tenantId) {
        return gray.isAllow("sales_record_objectification", tenantId);
    }

    /**
     * 检查租户是否允许更新客户的leads_id
     *
     * @param tenantId 租户ID
     * @return true 如果租户被允许更新leads_id
     */
    public static boolean isAllowUpdateLeadsId(String tenantId) {
        return gray.isAllow(UPDATE_LEADS_ID, tenantId);
    }
}
