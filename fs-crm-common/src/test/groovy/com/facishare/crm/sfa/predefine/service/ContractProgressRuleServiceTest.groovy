package com.facishare.crm.sfa.predefine.service

import com.facishare.crm.model.ContractProgressModel
import com.facishare.crm.sfa.BaseGroovyTest
import com.facishare.crm.sfa.predefine.service.task.SaleContractTaskService
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.impl.ObjectData
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import spock.lang.Shared
import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([I18N.class])
@SuppressStaticInitializationFor(["com.facishare.paas.I18N"])
class ContractProgressRuleServiceTest extends BaseGroovyTest {

    @Shared
    private ContractProgressRuleService contractProgressRuleService
    @Mock
    @Shared
    private ServiceFacade serviceFacade
    @Mock
    @Shared
    private SaleContractTaskService saleContractTaskService

    def setupSpec() {
        MockitoAnnotations.initMocks(this)
        contractProgressRuleService = new ContractProgressRuleService()
        Whitebox.setInternalState(contractProgressRuleService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(contractProgressRuleService, "saleContractTaskService", saleContractTaskService)
    }

    def setup() {
        PowerMockito.mockStatic(I18N.class)
    }

    def getTestUser() {
        return new User("90242", "1000")
    }

    def getRuleModel() {
        return ContractProgressModel.RuleModel.builder()
                .id("1").name("rule1").description("desc").enabledStatus(true)
                .contractRecordType(["type1"]).indexType("typeA").indexTypeObject("objA")
                .indexTypeObjectField("fieldA").indexTypeObjectFieldValue("valA")
                .indexGoalObject("goalObj").indexGoalObjectField("goalField")
                .indexGoalDataObject("goalDataObj").indexGoalDataDirectObject("goalDataDirObj")
                .indexGoalDataObjectField("goalDataField")
                .indexGoalDataRefContractField("refContractField")
                .indexGoalDataRefProductField("refProductField")
                .indexGoalDataRefTimeField("refTimeField")
                .indexGoalDataCondition("cond")
                .build();
    }
    def "test saveRule success"() {
        given:
        def user = getTestUser()
        def rule = getRuleModel();
        def mockData = new ObjectData(["_id":"111", "tenant_id":"90242"]);
        PowerMockito.doReturn(mockData).when(serviceFacade).saveObjectData(any(User), any(IObjectData))

        when:
        def result = contractProgressRuleService.saveRule(user, rule)

        then:
        result.success
        result.msg == "ok"
    }

    def "test editRule with not found id throws exception"() {
        given:
        def user = getTestUser()
        def rule = getRuleModel()
        rule.setId(null)
        when:
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.when(I18N.text(anyString())).thenReturn("mock text")
        contractProgressRuleService.editRule(user, rule)
        then:
        ValidateException exception = thrown(ValidateException)
        exception.getMessage() == "mock text";
    }

    def "test editRule with not found data throws exception"() {
        given:
        def user = getTestUser()
        def rule = getRuleModel()

        when:
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.when(I18N.text(anyString())).thenReturn("mock text")
        PowerMockito.doReturn(null).when(serviceFacade, "findObjectData", any(User), any(String), any(String))

        contractProgressRuleService.editRule(user, rule)

        then:
        ValidateException exception = thrown(ValidateException)
        exception.getMessage() == "mock text";
    }

//    def "test editRule success"() {
//        given:
//        def user = getTestUser()
//        def rule = ContractProgressModel.RuleModel.builder().id("1").name("rule1").build()
//        def mockData = ObjectDataExt.of(["_id":"111", "tenant_id":"90242"]).getObjectData()
//
//        when:
//        PowerMockito.doReturn(mockData).when(serviceFacade, "findObjectData", any(User), any(String), any(String))
//        PowerMockito.doReturn(mockData).when(serviceFacade, "updateObjectData", any(User), any(IObjectData))
//
//        def result = contractProgressRuleService.editRule(user, rule)
//
//        then:
//        result.success
//        result.msg == "ok"
//    }

    def "test invalidRule with blank id throws exception"() {
        given:
        def user = getTestUser()
        def rule = ContractProgressModel.RuleModel.builder().id("").build()

        when:
        contractProgressRuleService.invalidRule(user, rule)

        then:
        thrown(ValidateException)
    }

//    def "test queryRuleList empty result"() {
//        given:
//        def user = getTestUser()
//        def arg = new ContractProgressModel.RuleListModel(objectDescribeApiName: "descApi", searchQueryInfo: null)
//        PowerMockito.doReturn(null).when(serviceFacade).findBySearchQuery(any(User), anyString(), any())
//
//        when:
//        def result = contractProgressRuleService.queryRuleList(user, arg)
//
//        then:
//        result.success
//        result.data == []
//    }
//
//    def "test sendTask should call saleContractTaskService"() {
//        given:
//        def user = getTestUser()
//        def dataList = [Mock(IObjectData)]
//        def contractId = "c1"
//
//        when:
//        contractProgressRuleService.sendTask(user, dataList, contractId)
//
//        then:
//        1 * saleContractTaskService.createOrUpdateTask(user.getTenantId(), dataList, contractId)
//    }
//
//    def "test batchSaveGoalAndCheckPoint with empty progressGoalList"() {
//        given:
//        def user = getTestUser()
//        def arg = ContractProgressModel.RuleGoalModel.builder().progressGoalList([]).contractId("c1").toCompleteTime(System.currentTimeMillis() + 100000).build()
//        PowerMockito.doNothing().when(serviceFacade).bulkDeleteDirect(any(), any())
//        PowerMockito.doNothing().when(saleContractTaskService).deleteTask(any(), any())
//        PowerMockito.doReturn([]).when(serviceFacade).findBySearchQuery(any(), any(), any())
//
//        when:
//        def result = contractProgressRuleService.batchSaveGoalAndCheckPoint(user, arg, "saleName")
//
//        then:
//        result.success
//        result.data == []
//    }
//
//    def "test batchSaveGoalAndCheckPoint with invalid toCompleteTime"() {
//        given:
//        def user = getTestUser()
//        def arg = ContractProgressModel.RuleGoalModel.builder().progressGoalList([Mock(ContractProgressModel.ProgressGoalArg)]).contractId("c1").toCompleteTime(System.currentTimeMillis() - 100000).build()
//
//        when:
//        contractProgressRuleService.batchSaveGoalAndCheckPoint(user, arg, "saleName")
//
//        then:
//        thrown(ValidateException)
//    }

    def "test batchSaveGoalAndCheckPoint with invalid contractId"() {
        given:
        def user = getTestUser()
        def arg = ContractProgressModel.RuleGoalModel.builder().progressGoalList([Mock(ContractProgressModel.ProgressGoalArg)]).contractId("").toCompleteTime(System.currentTimeMillis() + 100000).build()

        when:
        contractProgressRuleService.batchSaveGoalAndCheckPoint(user, arg, "saleName")

        then:
        thrown(ValidateException)
    }

//    def "test batchSaveGoalAndCheckPoint with valid data"() {
//        given:
//        def user = getTestUser()
//        def checkArg = ContractProgressModel.RuleGoalCheckArg.builder().id("chk1").goalValue(new BigDecimal("10")).toCheckTime(System.currentTimeMillis() + 100000).build()
//        def progressGoalArg = ContractProgressModel.ProgressGoalArg.builder().id("goal1").ruleId("rule1").productId("prod1").goalValue(new BigDecimal("100")).checkList([checkArg]).build()
//        def arg = ContractProgressModel.RuleGoalModel.builder().progressGoalList([progressGoalArg]).contractId("c1").toCompleteTime(System.currentTimeMillis() + 100000).build()
//        PowerMockito.doReturn([:]).when(serviceFacade).findBySearchQuery(any(), any(), any())
//        PowerMockito.doNothing().when(serviceFacade).bulkSaveObjectData(any(), any())
//        PowerMockito.doNothing().when(serviceFacade).batchUpdateByFields(any(), any(), any())
//        PowerMockito.doNothing().when(serviceFacade).bulkDeleteDirect(any(), any())
//        PowerMockito.doNothing().when(saleContractTaskService).deleteTask(any(), any())
//        PowerMockito.doNothing().when(saleContractTaskService).createOrUpdateTask(any(), any(), any())
//        PowerMockito.doReturn(ContractProgressModel.Result.builder().msg("ok").success(true).data([]).build()).when(contractProgressRuleService).queryGoalProgressByContractId(any(), any(), any())
//        PowerMockito.suppress(PowerMockito.methods(ContractProgressRuleService, "buildAllGoalAndCheckPointMap"))
//        PowerMockito.replace(PowerMockito.method(ContractProgressRuleService, "buildAllGoalAndCheckPointMap")).with { u, a -> [goalAdd: [Mock(IObjectData)], goalUpdate: [], goalDelete: [], goalCheckAdd: [], goalCheckUpdate: [], goalCheckDelete: []] }
//        PowerMockito.replace(PowerMockito.method(ContractProgressRuleService, "queryGoalProgressByContractId")).with { u, a, s -> ContractProgressModel.Result.builder().msg("ok").success(true).data([]).build() }
//
//        when:
//        def result = contractProgressRuleService.batchSaveGoalAndCheckPoint(user, arg, "saleName")
//
//        then:
//        result.success
//        result.msg == "ok"
//    }

    def "test queryGoalProgressByContractId with blank contractId throws exception"() {
        given:
        def user = getTestUser()
        def arg = ContractProgressModel.ViewGoalModel.builder().contractId("").build()

        when:
        contractProgressRuleService.queryGoalProgressByContractId(user, arg, "saleName")

        then:
        thrown(ValidateException)
    }

//    def "test queryGoalProgressByContractId with empty result"() {
//        given:
//        def user = getTestUser()
//        def arg = ContractProgressModel.ViewGoalModel.builder().contractId("c1").build()
//        PowerMockito.replace(PowerMockito.method(CommonSearchUtil, "findDataBySearchQuery")).with { u, api, q -> [] }
//
//        when:
//        def result = contractProgressRuleService.queryGoalProgressByContractId(user, arg, "saleName")
//
//        then:
//        result.success
//        result.data == []
//    }

//    def "test queryGoalProgressByContractId with data and needCalculate and includeCheck"() {
//        given:
//        def user = getTestUser()
//        def arg = ContractProgressModel.ViewGoalModel.builder().contractId("c1").needCalculate(true).includeCheck(true).build()
//        def mockData = Mock(IObjectData)
//        PowerMockito.replace(PowerMockito.method(CommonSearchUtil, "findDataBySearchQuery")).with { u, api, q -> [mockData] }
//        PowerMockito.suppress(PowerMockito.methods(ContractProgressRuleService, "calculateCurrentGoal", "fillCheckPointInfo", "fillGoalReferenceLabel"))
//        PowerMockito.replace(PowerMockito.method(ObjectDataDocument, "ofList")).with { l -> l }
//
//        when:
//        def result = contractProgressRuleService.queryGoalProgressByContractId(user, arg, "saleName")
//
//        then:
//        result.success
//        result.data.size() == 1
//    }

    def "test createSnapshot with blank params throws exception"() {
        given:
        def user = getTestUser()
        def arg = ContractProgressModel.SnapshotArg.builder().contractId("").ruleGoalId("").checkPointId("").tenantId("").build()

        when:
        contractProgressRuleService.createSnapshot(user, arg)

        then:
        thrown(ValidateException)
    }

//    def "test createSnapshot with empty goalList does nothing"() {
//        given:
//        def user = getTestUser()
//        def arg = ContractProgressModel.SnapshotArg.builder().contractId("c1").ruleGoalId("r1").checkPointId("cp1").tenantId("t1").build()
//        PowerMockito.doReturn([]).when(serviceFacade).findObjectDataByIds(any(), any(), any())
//
//        when:
//        contractProgressRuleService.createSnapshot(user, arg)
//
//        then:
//        noExceptionThrown()
//    }
//
//    def "test createSnapshot with valid data"() {
//        given:
//        def user = getTestUser()
//        def arg = ContractProgressModel.SnapshotArg.builder().contractId("c1").ruleGoalId("r1").checkPointId("cp1").tenantId("t1").build()
//        def mockGoal = Mock(IObjectData)
//        def mockCheck = Mock(IObjectData)
//        PowerMockito.doReturn([mockGoal]).when(serviceFacade).findObjectDataByIds(any(), any(), any())
//        PowerMockito.doReturn([mockCheck]).when(serviceFacade).findObjectDataByIds(any(), any(), any())
//        PowerMockito.doNothing().when(serviceFacade).batchUpdateByFields(any(), any(), any())
//        PowerMockito.doNothing().when(serviceFacade).saveObjectData(any(), any())
//        PowerMockito.suppress(PowerMockito.methods(ContractProgressRuleService, "calculateCurrentGoal"))
//        PowerMockito.replace(PowerMockito.method(ObjectDataDocument, "of")).with { d -> d }
//        PowerMockito.replace(PowerMockito.method(ObjectDataDocument, "ofList")).with { l -> l }
//        PowerMockito.replace(PowerMockito.method(mockGoal, "get")).with { k, t, d -> k == "goal_value" ? new BigDecimal("100") : (k == "current_value" ? new BigDecimal("50") : null) }
//        PowerMockito.replace(PowerMockito.method(mockCheck, "set")).with { k, v -> }
//
//        when:
//        contractProgressRuleService.createSnapshot(user, arg)
//
//        then:
//        noExceptionThrown()
//    }
} 