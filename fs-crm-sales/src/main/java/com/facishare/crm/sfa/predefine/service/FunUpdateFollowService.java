package com.facishare.crm.sfa.predefine.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.enums.ActionCodeEnum;
import com.facishare.crm.sfa.predefine.service.model.UpdateFollowModel;
import com.facishare.crm.sfa.predefine.service.task.RecalculateTaskService;
import com.facishare.crm.sfa.task.RecalculateProducer;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.facishare.crm.sfa.utilities.constant.AccountConstants.Field.LAST_FOLLOWED_TIME;
import static com.facishare.crm.sfa.utilities.constant.AccountConstants.Field.EXPIRE_TIME;
import static com.facishare.crm.sfa.utilities.constant.LeadsConstants.LAST_FOLLOW_TIME;

@ServiceModule("follow")
@Component
@Slf4j
public class FunUpdateFollowService {

    @Autowired
    @Qualifier("objectDataPgService")
    private IObjectDataService objectDataService;

    @Autowired
    private RecalculateTaskService recalculateTaskService;
    
    @Autowired
    private RecalculateProducer recalculateProducer;

    @ServiceMethod("calculateExpireTime")
    public UpdateFollowModel.Result calculateExpireTime(ServiceContext context, UpdateFollowModel.Arg arg) {
        UpdateFollowModel.Result result = UpdateFollowModel.Result.builder().build();
        if (arg.getApiName() == null
                || (!Utils.ACCOUNT_API_NAME.equals(arg.getApiName()) && !Utils.LEADS_API_NAME.equals(arg.getApiName()))) {
            result.setMsg("apiName is illegal, only support AccountObj and LeadsObj");
            return result;
        }
        if (CollectionUtils.isEmpty(arg.getObjectIds())){
            result.setMsg("objectIds is empty");
            return result;
        }

        List<String> objectIds = arg.getObjectIds();
        List<IObjectData> objectDataList = Lists.newArrayList();
        ArrayList<String> updateFields = Lists.newArrayList();
        objectIds.forEach(
                objectId -> {
                    IObjectData data = new ObjectData();
                    data.setTenantId(context.getTenantId());
                    data.setDescribeApiName(arg.getApiName());
                    if (Utils.LEADS_API_NAME.equals(arg.getApiName())) {
                        data.set(LAST_FOLLOW_TIME, System.currentTimeMillis());
                        updateFields.add(LAST_FOLLOW_TIME);
                    } else {
                        data.set(LAST_FOLLOWED_TIME, System.currentTimeMillis());
                        updateFields.add(LAST_FOLLOWED_TIME);
                    }
                    data.setId(objectId);
                    objectDataList.add(data);
                }
        );

        try {
            objectDataService.batchUpdateIgnoreOther(objectDataList, updateFields, ActionContextExt.of(context.getUser()).getContext());
        } catch (MetadataServiceException e) {
            log.error("calculateExpireTime error", e);
        }
        for (String objectId : objectIds) {
            recalculateTaskService.send(context.getTenantId(), objectId, arg.getApiName(), ActionCodeEnum.FOLLOW);
        }
        return result;
    }
    
    /**
     * 更新客户、线索的到期时间并发送定时任务MQ
     * 
     * @param context 服务上下文
     * @param arg 参数，包含apiName、objectIds和expireTime
     * @return 执行结果
     */
    @ServiceMethod("updateExpireTimeAndSendMQ")
    public UpdateFollowModel.Result updateExpireTimeAndSendMQ(ServiceContext context, UpdateFollowModel.Arg arg) {
        UpdateFollowModel.Result result = UpdateFollowModel.Result.builder().build();
        
        // 参数校验
        if (arg.getApiName() == null
                || (!Utils.ACCOUNT_API_NAME.equals(arg.getApiName()) && !Utils.LEADS_API_NAME.equals(arg.getApiName()))) {
            result.setMsg("apiName is illegal, only support AccountObj and LeadsObj");
            return result;
        }
        if (CollectionUtils.isEmpty(arg.getObjectIds())) {
            result.setMsg("objectIds is empty");
            return result;
        }
        if (arg.getExpireTime() == null) {
            result.setMsg("expireTime is required");
            return result;
        }
        
        List<String> objectIds = arg.getObjectIds();
        List<IObjectData> objectDataList = Lists.newArrayList();
        ArrayList<String> updateFields = Lists.newArrayList();
        
        // 1. 更新到期时间
        objectIds.forEach(objectId -> {
            IObjectData data = new ObjectData();
            data.setTenantId(context.getTenantId());
            data.setDescribeApiName(arg.getApiName());
            
            // 更新到期时间字段
            data.set(EXPIRE_TIME, arg.getExpireTime().getTime());
            updateFields.add(EXPIRE_TIME);
            
            // 同时更新最后跟进时间
            if (Utils.LEADS_API_NAME.equals(arg.getApiName())) {
                data.set(LAST_FOLLOW_TIME, System.currentTimeMillis());
                updateFields.add(LAST_FOLLOW_TIME);
            } else {
                data.set(LAST_FOLLOWED_TIME, System.currentTimeMillis());
                updateFields.add(LAST_FOLLOWED_TIME);
            }
            
            data.setId(objectId);
            objectDataList.add(data);
        });
        
        try {
            // 批量更新数据
            objectDataService.batchUpdateIgnoreOther(objectDataList, updateFields, 
                    ActionContextExt.of(context.getUser()).getContext());
            
            // 2. 发送定时任务MQ（类似sendRecycling）
            for (String objectId : objectIds) {
                // 发送超时跟进的定时任务
                recalculateProducer.sendOverTimeTask(context.getTenantId(), objectId, arg.getExpireTime());
                
                // 同时触发重新计算
                recalculateTaskService.send(context.getTenantId(), objectId, arg.getApiName(), ActionCodeEnum.FOLLOW);
            }
            
            result.setSuccess(true);
            result.setMsg("Successfully updated expire time and sent MQ for " + objectIds.size() + " objects");
            
        } catch (MetadataServiceException e) {
            log.error("updateExpireTimeAndSendMQ error", e);
            result.setSuccess(false);
            result.setMsg("Failed to update expire time: " + e.getMessage());
        } catch (Exception e) {
            log.error("updateExpireTimeAndSendMQ send MQ error", e);
            result.setSuccess(false);
            result.setMsg("Failed to send MQ: " + e.getMessage());
        }
        
        return result;
    }
}
